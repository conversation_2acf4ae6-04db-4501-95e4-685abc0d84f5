[package]
name = "hypertrader-services"
version = "0.1.0"
edition = "2021"

[dependencies]
hypertrader-utils = { path = "../hypertrader-utils" }
hyperliquid_rust_sdk = { workspace = true }
hypertrader-clickhouse = { workspace = true }
hypertrader-data = { path = "../hypertrader-data" }
hypertrader-models = { path = "../hypertrader-models" }
hypertrader-hyperliquid = { path = "../hypertrader-hyperliquid" }
anyhow = "1.0"

dashmap = { workspace = true }
tokio = { version = "1", features = ["full"] }
tracing = "0.1"

chrono = "0.4"
futures = "0.3"
clap = { version = "4.0", features = ["derive"] }
tracing-subscriber = { workspace = true }

kanal = { version = "0.1.1", features = ["async"] }