//! # TableOptimizer
//!
//! `TableOptimizer` is a utility for managing and scheduling optimization of ClickHouse database tables.
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

use tokio::time;
use anyhow::Result;
use tracing::{info, warn, error};

use hypertrader_clickhouse::{client::ClickHouseClient, init_clickhouse_client};
use hypertrader_utils::env::GLOBAL_ENVS;


/// A struct representing a table optimizer for a ClickHouse database.
///
/// The `TableOptimizer` is responsible for performing optimization tasks
/// on specified tables within a ClickHouse database. It holds a reference
/// to a ClickHouse client for executing operations and optionally specifies
/// a target database for context.
///
/// # Fields
///
/// * `clickhouse_client` - An `Arc<ClickHouseClient>` which provides the ability
///   to execute commands and queries against the ClickHouse database.
/// * `database` - An `Option<String>` representing the database for which tables
///   will be optimized. If `None`, the default database configured on the
///   ClickHouse client will be used.
///
/// # Example Usage
///
/// ```rust
/// use std::sync::Arc;
///
/// // Assume that `ClickHouseClient` is initialized properly:
/// let clickhouse_client = Arc::new(ClickHouseClient::new());
///
/// // Create a new TableOptimizer instance:
/// let table_optimizer = TableOptimizer {
///     clickhouse_client,
///     database: Some("example_database".to_string()),
/// };
/// ```
///
/// # Note
///
/// This struct is a utility that assumes proper setup of the `ClickHouseClient`
/// and the database schema. Ensure that any database or table operations
/// follow the permissions and configurations of the ClickHouse server.
pub struct TableOptimizer {
    clickhouse_client: Arc<ClickHouseClient>,
    database: Option<String>,
}


impl TableOptimizer {
    /// Creates and initializes a new instance of the struct, asynchronously setting up the required ClickHouse client connection.
    ///
    /// # Returns
    /// * `Ok(Self)` - If the `ClickHouseClient` is successfully initialized and the environment variable for 
    ///   the database (`clickhouse_db`) is retrieved.
    /// * `Err` - If initializing the `ClickHouseClient` fails.
    ///
    /// # Implementation Details
    /// - The function asynchronously initializes a `ClickHouseClient` by calling `init_clickhouse_client`.
    /// - Extracts the ClickHouse database name from the global environment variables (`GLOBAL_ENVS`).
    /// - Wraps the `clickhouse_client` in an `Arc` for thread-safe shared ownership.
    ///
    /// # Errors
    /// Returns an error if the `init_clickhouse_client` async call fails.
    ///
    /// # Examples
    /// ```rust
    /// let instance = MyStruct::new().await?;
    /// ```
    pub async fn new() -> Result<Self> {
        let clickhouse_client: ClickHouseClient = init_clickhouse_client().await?;
        let database = GLOBAL_ENVS.clickhouse_db();
        Ok(Self {
            clickhouse_client: Arc::new(clickhouse_client),
            database: database.map(|db| db.to_string()),
        })
    }

    /// Asynchronously schedules and runs optimization tasks for tables based on predefined timeframes.
    ///
    /// ## Description
    /// This function sets up scheduled optimize operations on different timeframes' tables. 
    /// Each timeframe (e.g., "1m", "5m", "1h", "1d") is assigned a specific optimization interval, 
    /// determining how often optimization tasks are executed for that table. All tasks run concurrently.
    ///
    /// The function performs the following steps:
    /// 1. Defines a mapping between timeframes and their corresponding optimization intervals using a `HashMap`.
    /// 2. For each timeframe, spawns a `tokio::spawn` task that:
    ///     - Waits for the interval duration before running optimizations.
    ///     - Executes optimization on the specific table using the `optimize_specific_table` method of the ClickHouse client.
    ///     - Logs either the success or failure of the optimization.
    ///
    /// The function ensures that optimization is performed repeatedly on all tables for the respective timeframes 
    /// at their configured intervals.
    ///
    /// ## Parameters
    /// - `self`: A reference to the instance of the struct implementing this function. It assumes that `self` provides:
    ///   - `clickhouse_client`: An async client for interacting with a ClickHouse database.
    ///   - `database`: The name of the target database for the optimization tasks.
    ///
    /// ## Returns
    /// - `Result<()>`: Indicates success or failure of the overall function execution. Any potential errors primarily arise 
    ///   during the initialization of tasks.
    ///
    /// ## Logs
    /// - Logs information messages when optimization is scheduled and after successful table optimizations.
    /// - Logs error messages when table optimization fails.
    ///
    /// ## Timeframes and Their Optimization Intervals
    /// The following timeframes are supported, each with its respective optimization interval:
    /// | Timeframe | Optimization Interval |
    /// |-----------|-----------------------|
    /// | "1m"      | 1 minute              |
    /// | "3m"      | 3 minutes             |
    /// | "5m"      | 5 minutes             |
    /// | "15m"     | 15 minutes            |
    /// | "30m"     | 30 minutes            |
    /// | "1h"      | 1 hour                |
    /// | "2h"      | 2 hours               |
    /// | "4h"      | 4 hours               |
    /// | "8h"      | 8 hours               |
    /// | "12h"     | 12 hours              |
    /// | "1d"      | 1 day                 |
    /// | "3d"      | 3 days                |
    /// | "1w"      | 7 days                |
    /// | "1M"      | 1 month               |
    ///
    /// ## Notes
    /// - Each spawned task operates independently for each timeframe, utilizing `tokio::time::interval` to schedule executions.
    /// - The table names follow a naming convention: `ohlc_<timeframe>`, where `<timeframe>` corresponds to the timeframe string.
    ///
    /// ## Example Usage
    /// ```rust
    /// let manager = OptimizationManager {
    ///     clickhouse_client: my_clickhouse_client,
    ///     database: Some("my_db_name".to_string()),
    /// };
    ///
    /// if let Err(e) = manager.run().await {
    ///     eprintln!("Failed to schedule optimization tasks: {}", e);
    /// }
    /// ```
    pub async fn run(&self) -> Result<()> {
        // Define timeframes and their optimization intervals
        let timeframes_with_intervals: HashMap<&str, Duration> = [
            // Smaller timeframes optimize more frequently
            ("1m", Duration::from_secs(60)), // Every minute
            ("3m", Duration::from_secs(60 * 3)), // Every 3 minutes
            ("5m", Duration::from_secs(60 * 5)), // Every 5 minutes
            ("15m", Duration::from_secs(60 * 15)), // Every 15 minutes
            ("30m", Duration::from_secs(60 * 30)), // Every 30 minutes
            ("1h", Duration::from_secs(3600)), // Every hour
            ("2h", Duration::from_secs(3600 * 2)), // Every 2 hours
            ("4h", Duration::from_secs(3600 * 4)), // Every 4 hours
            ("8h", Duration::from_secs(3600 * 8)), // Every 8 hours
            ("12h", Duration::from_secs(3600 * 12)), // Every 12 hours
            ("1d", Duration::from_secs(3600 * 24)), // Every 24 hours
            ("3d", Duration::from_secs(3600 * 24 * 3)), // Every 3 days
            ("1w", Duration::from_secs(3600 * 24 * 7)), // Every 7 days
            ("1M", Duration::from_secs(3600 * 24 * 7)), // Every 7 days
        ].iter().cloned().collect();

        // Create a separate task for each timeframe
        for (timeframe, interval) in timeframes_with_intervals {
            let client = self.clickhouse_client.clone();

            let db = self.database.clone();

            // Spawn a task for this timeframe
            tokio::spawn(async move {
                let db_ref = db.as_deref();
                let mut interval_timer = time::interval(interval);

                loop {
                    interval_timer.tick().await;

                    let table_name = format!("ohlc_{}", timeframe);
                    info!("Running scheduled optimization for table {}", table_name);

                    match client.optimize_specific_table(&table_name, db_ref).await {
                        Ok(_) => info!("Successfully optimized table {}", table_name),
                        Err(e) => error!("Failed to optimize table {}: {}", table_name, e),
                    }
                }
            });

            info!("Scheduled optimization for ohlc_{} every {} seconds", timeframe, interval.as_secs());
        }

        Ok(())
    }

    /// Optimizes all relevant database tables immediately.
    ///
    /// This asynchronous function processes a predefined list of tables,
    /// specifically OHLC (Open-High-Low-Close) tables for various timeframes
    /// (e.g., 1 minute, 5 minutes, 15 minutes, etc.), and a "klines" table.
    /// It uses the `optimize_specific_table` method of the `clickhouse_client`
    /// to optimize these tables in a ClickHouse database, improving their performance 
    /// and storage efficiency.
    ///
    /// # Timeframes
    /// The list of OHLC timeframes includes the following:
    /// - 1 minute ("1m")
    /// - 5 minutes ("5m")
    /// - 15 minutes ("15m")
    /// - 30 minutes ("30m")
    /// - 1 hour ("1h")
    /// - 2 hours ("2h")
    /// - 4 hours ("4h")
    /// - 8 hours ("8h")
    /// - 12 hours ("12h")
    /// - 1 day ("1d")
    /// - 3 days ("3d")
    /// - 1 week ("1w")
    /// - 1 month ("1M")
    ///
    /// # Behavior
    /// - The function logs the optimization process for each table.
    /// - For each OHLC table, if optimization succeeds, it logs a success message.
    ///   If optimization fails, it logs a warning with the table name and error message.
    /// - After processing OHLC tables, the "klines" table is optimized similarly.
    ///
    /// # Logs
    /// - `info`: Indicates the start of the optimization process and successful optimization for each table.
    /// - `warn`: Indicates any failures during table optimization, detailing the specific table and the error.
    ///
    /// # Errors
    /// No specific errors are returned by this function; individual table optimization failures are logged,
    /// and the function proceeds with the next table. The function itself always returns `Ok(())`.
    ///
    /// # Returns
    /// A `Result`:
    /// - `Ok(())`: If all operations complete without producing unexpected errors (note that table-specific
    ///             errors are logged and do not interrupt execution).
    ///
    /// # Example Usage
    /// ```rust
    /// let optimizer = YourStruct { /* initialization */ };
    /// optimizer.optimize_all_now().await?;
    /// ```
    ///
    /// # Notes
    /// - The `clickhouse_client` and `self.database` are assumed to be properly initialized within the struct.
    /// - Ensure the necessary permissions and connection for optimizing ClickHouse tables are in place before invoking this method.
    pub async fn optimize_all_now(&self) -> Result<()> {
        let timeframes = [
            "1m", "5m", "15m", "30m",
            "1h", "2h", "4h", "8h", "12h",
            "1d", "3d", "1w", "1M"
        ];

        info!("Running immediate optimization for all tables");

        // Optimize OHLC tables
        for timeframe in timeframes.iter() {
            let table_name = format!("ohlc_{}", timeframe);
            match self.clickhouse_client.optimize_specific_table(&table_name, self.database.as_deref()).await {
                Ok(_) => info!("Successfully optimized table {}", table_name),
                Err(e) => warn!("Failed to optimize table {}: {}", table_name, e),
            }
        }

        // Optimize klines table
        match self.clickhouse_client.optimize_specific_table("klines", self.database.as_deref()).await {
            Ok(_) => info!("Successfully optimized klines table"),
            Err(e) => warn!("Failed to optimize klines table: {}", e),
        }

        Ok(())
    }
}