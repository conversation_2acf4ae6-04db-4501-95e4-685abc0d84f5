use anyhow::Result;
use chrono::{DateTime, Duration, Utc};

use hyperliquid_rust_sdk::AssetMeta;
use hypertrader_clickhouse::client::ClickHouseClient;
use hypertrader_clickhouse::init_clickhouse_client;
use hypertrader_clickhouse::models::kline::Kline;
use hypertrader_data::cex::hyperliquid::HyperliquidDataManager;
use hypertrader_models::kline::interval::KlineInterval;

/// Service to update historical OHLC data in ClickHouse
#[derive(Clone)]
pub struct RangeService {
    pub hyperliquid_data_manager: HyperliquidDataManager,
    max_fetch_minutes: i64,
    clickhouse_client: ClickHouseClient,
}

impl RangeService {
    /// Create a new RangeService
    pub async fn new(
        use_websocket: bool,
        max_fetch_minutes: i64,
    ) -> Result<Self> {
        let hyperliquid_data_manager = HyperliquidDataManager::new_without_postgres(use_websocket).await?;
        let clickhouse_client: ClickHouseClient = init_clickhouse_client().await?;
        Ok(Self {
            clickhouse_client,
            hyperliquid_data_manager,
            max_fetch_minutes,
        })
    }

    /// Initialize the service and database structures
    pub async fn init(&self) -> Result<()> {
        // Initialize Hyperliquid data manager
        self.hyperliquid_data_manager.init().await?;

        // Ensure the OHLC_1m table exists
        self.clickhouse_client
            .create_ohlc_table_and_view("1m", "1", "MINUTE")
            .await?;

        Ok(())
    }

    pub async fn fetch_and_update_ohlc_data(
        &self,
        subscribed_coins: Vec<AssetMeta>,
        days_to_fetch: i64,
        batch_size: usize,
    ) -> Result<()> {
        if subscribed_coins.is_empty() {
            tracing::info!("No subscribed coins, skipping OHLC data retrieval");
            return Ok(());
        }

        tracing::info!(
            "Starting batch retrieval and update of OHLC data for {} coins, batch size: {}",
            subscribed_coins.len(),
            batch_size
        );

        // Keep track of coins that still need processing
        let coins_to_process: dashmap::DashMap<String, ()> = dashmap::DashMap::new();
        for coin in &subscribed_coins {
            coins_to_process.insert(coin.name.clone(), ());
        }

        let mut retry_iters = 0;
        let max_retries = 5;

        // Continue until all coins are processed or max retries reached
        while coins_to_process.len() > 0 && retry_iters < max_retries {
            retry_iters += 1;
            // tracing::info!("Attempt {} to retrieve and update OHLC data", retry_iters);

            // Prepare coins list for processing
            let mut coins_to_fetch_vec: Vec<String> = coins_to_process
                .iter()
                .map(|entry| entry.key().clone())
                .collect();

            // Alternate order to distribute load
            if retry_iters % 2 == 0 {
                coins_to_fetch_vec.reverse();
            }

            // Process in batches
            for chunk in coins_to_fetch_vec.chunks(batch_size) {
                let mut tasks = Vec::with_capacity(batch_size);

                // Create tasks for current batch
                for symbol in chunk {
                    // tracing::debug!("Creating task to update OHLC data for {}", symbol);

                    let symbol = symbol.clone();
                    let coins_map = &coins_to_process;
                    let service = &self;

                    // Create a future for this coin's update
                    let task = async move {
                        match service.update_symbol_history(&symbol, days_to_fetch).await {
                            Ok(count) => {
                                if count > 0 {
                                    // Successfully processed data, remove from pending list
                                    coins_map.remove(&symbol);
                                    tracing::info!(
                                        "Successfully updated OHLC data for {}: {} records",
                                        symbol, count
                                    );
                                } else {
                                    tracing::warn!(
                                        "No OHLC records found for {}",
                                        symbol
                                    );
                                }
                                Ok(())
                            },
                            Err(e) => {
                                // tracing::error!(
                                //     "Failed to update OHLC data for {}: {}",
                                //     symbol, e
                                // );
                                Err(e)
                            }
                        }
                    };

                    tasks.push(task);
                }

                // Execute current batch tasks concurrently
                let _results = futures::future::join_all(tasks).await;
            }

            // tracing::info!("Remaining coins to update OHLC data: {}", coins_to_process.len());

            // Wait to ensure network stability for retries
            if coins_to_process.len() > 0 && retry_iters < max_retries {
                // Incremental wait time
                let wait_seconds = 8 * retry_iters;
                // tracing::info!("Waiting for {} seconds before next retry...", wait_seconds);
                tokio::time::sleep(std::time::Duration::from_secs(wait_seconds as u64)).await;
            }
        }

        if coins_to_process.len() > 0 {
            tracing::warn!(
                "Could not update OHLC data for {} coins after {} attempts",
                coins_to_process.len(),
                retry_iters
            );
        }

        let processed = subscribed_coins.len() - coins_to_process.len();
        tracing::info!(
            "Completed update of OHLC data for {}/{} coins",
            processed,
            subscribed_coins.len()
        );

        Ok(())
    }

    /// Get timestamp in milliseconds for a datetime
    fn get_epoch_millis(dt: &DateTime<Utc>) -> i64 {
        dt.timestamp_millis()
    }

    /// Fetch and store candles for a specific symbol and time range
    pub async fn fetch_and_store_candles(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>
    ) -> Result<usize> {
        // Convert to i64 for API call
        let start_millis = Self::get_epoch_millis(&start_time);
        let end_millis = Self::get_epoch_millis(&end_time);

        // Fetch candles from Hyperliquid API
        let candles = self.hyperliquid_data_manager.client
            .klines_history(
                symbol.to_string(),
                start_millis as u64,
                end_millis as u64,
                &KlineInterval::OneMinute,
            )
            .await?;

        // tracing::info!("Fetched {} candles for {}", candles.len(), symbol);

        if candles.is_empty() {
            return Ok(0);
        }

        // Create a kline batch for bulk insertion
        let mut kline_batch = hypertrader_clickhouse::models::kline::KlineBatch::new();

        // Convert and add each candle to the batch
        for candle in candles {
            let kline = Kline {
                symbol: candle.coin.clone(),
                interval: candle.candle_interval.clone(),
                time_open: candle.time_open as i64,
                time_close: candle.time_close as i64,
                open: candle.open.parse().unwrap_or_default(),
                high: candle.high.parse().unwrap_or_default(),
                low: candle.low.parse().unwrap_or_default(),
                close: candle.close.parse().unwrap_or_default(),
                volume: candle.vlm.parse().unwrap_or_default(),
                num_trades: candle.num_trades,
            };

            kline_batch.add(kline);

            // Log progress periodically
            if kline_batch.len() % 1000 == 0 {
                tracing::info!("Prepared {} candles for {}", kline_batch.len(), symbol);
            }
        }

        let processed = kline_batch.len();

        // Insert the batch into ClickHouse
        // tracing::info!("Inserting batch of {} candles for {}", processed, symbol);
        self.clickhouse_client.insert_ohlc_batch("ohlc_1m", kline_batch).await?;

        // tracing::info!("Successfully processed {} candles for {}", processed, symbol);
        Ok(processed)
    }

    /// Update historical data for a symbol, fetching data in chunks
    pub async fn update_symbol_history(
        &self,
        symbol: &str,
        days_to_fetch: i64,
    ) -> Result<usize> {
        let now = Utc::now();
        let start_date = now - Duration::days(days_to_fetch);
        let mut current_start = start_date;
        let mut total_processed = 0;

        // Fetch data in chunks defined by max_fetch_minutes
        while current_start < now {
            let chunk_end = (current_start + Duration::minutes(self.max_fetch_minutes)).min(now);

            let processed = self.fetch_and_store_candles(
                symbol,
                current_start,
                chunk_end,
            ).await?;

            total_processed += processed;

            // Move to next chunk
            current_start = chunk_end;
        }

        tracing::info!("Completed historical update for {}/{} records processed", symbol, total_processed);
        Ok(total_processed)
    }
}