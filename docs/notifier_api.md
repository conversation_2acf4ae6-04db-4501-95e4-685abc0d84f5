# WebSocket通知系统API文档

本文档详细介绍了WebSocket通知系统的API使用方法，这是一个简单实用的模板示例，可以作为开发各种实时通知功能的基础。

## 消息格式

所有WebSocket消息遵循以下JSON格式：

```json
{
  "type": "消息类型",
  "data": {
    // 消息内容，根据类型不同而变化
  }
}
```

## 基础消息类型

系统内置的基础消息类型包括：

- `Connect`: 连接确认消息
- `Subscribe`: 订阅频道
- `Unsubscribe`: 取消订阅
- `Error`: 错误消息
- `Ping`/`Pong`: 心跳消息
- `Close`: 关闭连接消息
- `Custom`: 自定义业务消息

## 通知系统API

通知系统使用`Custom`消息类型，通过`event`字段区分不同的业务操作。

### 1. 获取通知列表

**请求：**

```json
{
  "type": "Custom",
  "data": {
    "event": "get_notifications",
    "data": {
      "user_id": "user1",
      "since": "2023-01-01T00:00:00Z",  // 可选，ISO8601格式
      "priority": "High",  // 可选，通知优先级过滤
      "limit": 10  // 可选，限制返回数量
    }
  }
}
```

**响应：**

```json
{
  "type": "Custom",
  "data": {
    "event": "notifications",
    "data": {
      "notifications": [
        {
          "id": "9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d",
          "title": "系统更新通知",
          "content": "系统将于今晚22:00进行更新维护",
          "priority": "High",
          "created_at": "2023-03-15T14:30:00Z",
          "read": false
        },
        // 更多通知...
      ],
      "total": 1
    }
  }
}
```

### 2. 标记通知为已读

**请求：**

```json
{
  "type": "Custom",
  "data": {
    "event": "mark_notification_read",
    "data": {
      "notification_id": "9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d"
    }
  }
}
```

**响应：**

```json
{
  "type": "Custom",
  "data": {
    "event": "notification_marked_read",
    "data": {
      "notification_id": "9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d",
      "success": true
    }
  }
}
```

### 3. 接收新通知

服务器主动推送的新通知：

```json
{
  "type": "Custom",
  "data": {
    "event": "notification",
    "data": {
      "id": "9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d",
      "title": "新消息提醒",
      "content": "您有一条新消息",
      "priority": "Normal",
      "created_at": "2023-03-15T14:30:00Z",
      "read": false
    }
  }
}
```

### 4. 错误响应

当请求处理出错时：

```json
{
  "type": "Error",
  "data": {
    "code": 404,
    "message": "通知未找到"
  }
}
```

## 通知优先级

通知优先级分为以下几种：

- `Low`: 低优先级，一般信息
- `Normal`: 普通优先级，默认级别
- `High`: 高优先级，重要信息
- `Urgent`: 紧急，需要立即处理

## 使用示例

### 前端连接示例

```javascript
const socket = new WebSocket('ws://localhost:8080/ws?client_id=user1');

socket.onopen = function() {
    console.log('已连接到WebSocket服务器');
};

socket.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);

    // 处理不同类型的消息
    if (message.type === 'Custom') {
        const { event, data } = message.data;
        switch (event) {
            case 'notifications':
                // 渲染通知列表
                renderNotifications(data.notifications);
                break;
            case 'notification':
                // 显示新通知
                showNotificationAlert(data);
                break;
        }
    }
};

// 获取通知列表
function getNotifications() {
    const message = {
        type: 'Custom',
        data: {
            event: 'get_notifications',
            data: {
                user_id: 'user1',
                limit: 10
            }
        }
    };
    socket.send(JSON.stringify(message));
}

// 标记通知为已读
function markAsRead(notificationId) {
    const message = {
        type: 'Custom',
        data: {
            event: 'mark_notification_read',
            data: {
                notification_id: notificationId
            }
        }
    };
    socket.send(JSON.stringify(message));
}
```

### 使用REST API创建通知

虽然通知的接收和管理通过WebSocket进行，但创建通知通常通过后端API完成：

```
POST /api/notifications

{
  "user_id": "user1",
  "title": "系统通知",
  "content": "您的账户即将到期",
  "priority": "High"
}
```

然后服务器会通过WebSocket将通知推送给目标用户。

## 扩展建议

这个简单的通知系统可以轻松扩展以支持:

1. 通知分组/分类
2. 通知批量操作（全部标记为已读等）
3. 通知持久化和历史记录
4. 通知偏好设置（用户可以选择接收哪些类型的通知）
5. 通知量化指标统计

这个模板提供了所有关键功能，便于你根据具体业务需求快速扩展。 