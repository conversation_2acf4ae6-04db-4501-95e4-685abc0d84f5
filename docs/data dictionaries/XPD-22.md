### Vault Daily Snapshot / 金库每日快照
======================================

This data structure defines the daily snapshot of vault status for performance tracking and time series analysis. (UTC)
此数据结构定义了策略金库每日状态的快照，用于策略表现回测、趋势分析和可视化图表等。(UTC)

Attributes:
-----------
    strategy_uuid (str, foreign_key):  
        Identifier of the strategy.  
        策略金库标识符。  
        Example: "62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"

    date (str):  
        Snapshot date in ISO format.  
        快照日期（ISO 格式）。  
        Example: "2025-03-21"

    vault_value_usd (float):  
        Total vault value at end of day.  
        当日金库总价值。  
        Example: 15823.00

    realized_pnl_usd (float):  
        Realized PnL generated within the day.  
        当日已实现盈亏。  
        Example: 225.50

    unrealized_pnl_usd (float):  
        Floating PnL at end of day.  
        当日浮动盈亏。  
        Example: 40.25

    trading_volume_usd (float):  
        Daily trading volume.  
        当日交易量。  
        Example: 15000.00

    open_position_count (int):  
        Number of open positions at end of day.  
        当日持仓数量。  
        Example: 2

    updated_at (str):  
        Timestamp of snapshot creation.  
        快照生成时间。  
        Example: "2025-03-21T23:59:59Z"


### Vault Position History / 金库历史持仓记录
===========================================

This data structure defines the historical lifecycle of all vault positions for backtesting and reporting.
此数据结构定义了金库所有持仓生命周期的完整历史记录，便于策略回测、报表生成与风险分析。

Attributes:
-----------
    position_id (str, unique):  
        Unique ID of the position.  
        仓位唯一标识。  
        Example: "pos_eth_00087"

    strategy_uuid (str, foreign_key):  
        Identifier of the strategy vault.  
        策略金库标识符。  
        Example: "62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"

    token (str):  
        Token symbol.  
        币种。  
        Example: "ETH-USDT"

    direction (str):  
        Direction of the position.  
        持仓方向。  
        Allowed values: "long", "short"

    entry_price (float):  
        Price at which the position was opened.  
        开仓价格。  
        Example: 3140.00

    exit_price (float):  
        Price at which the position was closed.  
        平仓价格。  
        Example: 3190.00

    position_size (float):  
        Notional position size.  
        仓位名义金额。  
        Example: 2000.00

    leverage (float):  
        Leverage used.  
        杠杆倍数。  
        Example: 10.0

    collateral_used (float):  
        Actual capital allocated.  
        使用的保证金。  
        Example: 200.00

    realized_pnl_usd (float):  
        Total realized profit/loss.  
        实现盈亏。  
        Example: 50.00

    signal_entry_id (str, foreign_key):  
        Entry signal ID.  
        开仓信号 ID。  
        Example: "sig_123abc"

    signal_exit_id (str, foreign_key, optional):  
        Exit signal ID.  
        平仓信号 ID。  
        Example: "sig_456def"

    opened_at (str):  
        Timestamp of position open.  
        开仓时间。  
        Example: "2025-03-20T09:00:00Z"

    closed_at (str):  
        Timestamp of position close.  
        平仓时间。  
        Example: "2025-03-20T10:30:00Z"


### Strategy Performance Metrics / 策略绩效评估
=============================================

This data structure defines performance evaluation for both real and virtual strategies.
此数据结构定义了实盘和虚拟盘策略的绩效评估，包括统计胜率、收益、连胜等指标。

Attributes:
-----------
    strategy_uuid (str, foreign_key):  
        Identifier of the strategy.  
        策略标识符。  
        Example: "62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"

    mode (str):  
        Type of evaluation.  
        评估类型。  
        Allowed values: "real", "virtual"  
        Example: "virtual"

    win_rate (float):  
        Ratio of profitable trades.  
        胜率（盈利交易占比）。  
        Example: 0.58

    recent_month_return_pct (float):  
        Net return over the past 30 days.  
        最近一个月收益（百分比）。  
        Example: 6.2

    total_return_pct (float):  
        Total return since inception.  
        累计总收益（百分比）。  
        Example: 32.4

    live_return_pct (float):  
        Live floating return based on current vault value.  
        实时收益（浮动）。  
        Example: 4.9

    btc_outperformance_pct (float):  
        30-day strategy return minus BTC 30-day return.  
        最近一个月跑赢BTC现货的幅度。  
        Example: 2.1

    longest_win_streak (int):  
        Longest consecutive wins.  
        连胜次数记录。  
        Example: 5

    sharpe_ratio (float):  
        Risk-adjusted return metric.  
        夏普比率。  
        Example: 1.42

    evaluation_period (str):  
        Aggregated evaluation time range (e.g., "30d", "all").  
        评估周期，如“近30天”、“全部历史”。  
        Example: "30d"

    updated_at (str):  
        Last updated timestamp.  
        更新时间。  
        Example: "2025-03-21T00:00:00Z"


### Metrics Calculation References / 指标计算说明
===============================================

#### Sharpe Ratio（夏普比率）计算公式

$$ Sharpe Ratio=  \frac{R_p - R_f}{\sigma_p}​$$

$Rp$ = return of portfolio 

$Rf$ = risk-free rate

$σp$ = standard deviation of the portfolio’s excess return

https://www.investopedia.com/terms/s/sharperatio.asp

    avg_daily_return: 策略平均日收益率
    risk_free_rate: 无风险利率（可设为 0 或 US Treasury 年化 / 365）
    std_daily_return: 策略收益率的标准差（波动率）

    sharpe_ratio = (avg_daily_return - risk_free_rate) / std_daily_return


#### BTC Race Formular 跑赢大盘计算（以BTC现货为基准）

$$ Outperformance = R_{strategy_30d} - R_{BTC_30d} $$

$R_strategy_30d$ = 策略近30天收益率

$𝑅_BTC_30d$ = BTC现货近30天涨幅