### HFT Vault & Strategy / 高频交易金库及策略
==========================================

This data structure defines the expected schema for setting up strategy for HFT bot.  
此数据结构定义了高频交易机器人（HFT bot）的策略配置格式。

Attributes:
-----------
    strategy_uuid (str, unique): 
        Identifier of the trading strategy group.  
        交易策略集群标识符。  
        Example: "62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"
        
    strategy_name (str): 
        Identifier name of the trading strategy group.  
        交易策略集群标识名称。  
        Example: "XBIT智选2号"
        
    vault_address (str, unique): 
        Wallet address of vault.  
        金库钱包地址。  
        Example: "******************************************"
        
    strategy_owner (str): 
        Wallet address of strategy owner.  
        策略所有者的钱包地址。  
        Example: "******************************************"
        
    preferred_strategy (boolean): 
        The preferred strategy will be sent to the front end as a signal indicator.  
        优选策略将作为信号指标发送给前端。  
        Example: True


### HFT Signal / 高频交易信号
===========================

This data structure defines the expected schema for incoming signals from an HFT bot.  
此数据结构定义了来自高频交易机器人（HFT bot）的信号格式。

Attributes:
-----------
    id (str, unique): 
        Signal ID.  
        信号ID。  
        Example: "72f27700-6f1e-4c43-b036-a91ae37d6929"
    
    strategy_uuid (str, foreign_key): 
        Identifier of the trading strategy group.  
        交易策略集群标识符。  
        Example: "62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"

    action (str): 
        Indicates whether the signal is to "buy" or "sell".  
        表示该信号是“买入”还是“卖出”。  
        Allowed values: "buy", "sell"

    token (str): 
        The trading token symbol or pair.  
        交易标的的代币符号或交易对。  
        Example: "ETH-USDT"

    current_price (float): 
        Market price at the time the signal is generated.  
        信号生成时的市场价格。  
        Example: 3842.75

    entry_price (float): 
        Suggested price to enter the trade.  
        建议的建仓价格。  
        Example: 3840.00

    take_profit (float): 
        Target price to close the trade for profit.  
        止盈目标价格。  
        Example: 3900.00

    stop_loss (float): 
        Price to exit the trade to prevent loss.  
        止损价格。  
        Example: 3800.00

    position_size (float, optional): 
        Notional value of the position.  
        仓位名义价值（可以是 USD 或代币单位）。  
        Example: 1000.0

    leverage (float, optional): 
        Leverage factor to apply to the position.  
        使用的杠杆倍数。  
        Example: 10.0

    status (str): 
        Current status of the signal.  
        信号的当前状态。  
        Allowed values: "pending", "executed", "cancelled", "expired"

    is_position_closing_signal (bool, optional): 
        Whether this signal is intended to close an open position.  
        该信号是否为平仓信号。  
        Example: True

    signal_time (str): 
        ISO 8601 timestamp when the signal was generated.  
        信号生成时间（ISO 8601 时间格式）。  
        Example: "2025-03-21T13:45:00Z"

    expires_at (str, optional): 
        Time when the signal becomes invalid.  
        信号过期时间（可选字段）。  
        Example: "2025-03-21T14:00:00Z"
        
    interval (str, optional): 
        Time interval the signal applies to (e.g. 1H, 4H, 1D).  
        信号所基于的时间周期。  
        Example: "1H"

    metadata (dict, optional): 
        Optional custom fields from strategy.  
        策略附加的自定义字段。  
        Example: {"confidence": 0.95}



### Vault Token Position / 金库币种持仓
=====================================

This data structure defines token-level position status for each strategy vault.  
此数据结构定义了策略金库的每个币种持仓状态。

Attributes:
-----------
    id (str, unique): 
        New submissions or changes will generate new IDs, ensuring that every operation of the trader is recorded.
        新提交或变更仓位将生成新ID，确保记录交易员每一次操作。  
        Example: "72f27700-6f1e-4c43-b036-a91ae37d6929"

    strategy_uuid (str, foreign_key): 
        Identifier of the strategy.  
        策略标识符。  
        Example: "62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"

    token (str): 
        Token symbol.  
        币种符号。  
        Example: "BTC-USDT"

    direction (str): 
        Position direction for this token.  
        当前币种持仓方向。  
        Allowed values: "long", "short"

    status (str): 
        Position status.  
        当前状态。  
        Allowed values: "open", "closing", "closed", "expired"

    position_size (float): 
        Notional position size.  
        当前名义仓位大小。  
        Example: 10000.0

    entry_price (float): 
        Entry price of the current position.  
        当前持仓的建仓价格。  
        Example: 27250.5

    current_price (float): 
        Latest market price of token.  
        最新市场价格。  
        Example: 27410.2

    leverage (float): 
        Leverage used.  
        杠杆倍数。  
        Example: 8.0

    collateral_used (float): 
        Vault funds allocated to this position.  
        分配至该仓位的资金。  
        Example: 1250.0

    unrealized_pnl_usd (float): 
        Unrealized profit/loss in USD.  
        未实现盈亏。  
        Example: 60.5

    realized_pnl_usd (float): 
        Realized PnL.  
        已实现盈亏。  
        Example: 15.0

    signal_id (str, foreign_key, optional): 
        Last signal that updated this position.  
        最后一个关联信号的ID。  
        Example: "72f27700-6f1e-4c43-b036-a91ae37d6929"

    opened_at (str): 
        Timestamp when position was opened.  
        仓位开启时间。  
        Example: "2025-03-21T13:45:00Z"

    updated_at (str): 
        Timestamp of last update.  
        最后更新时间。  
        Example: "2025-03-21T14:15:00Z"



### Vault Metrics Summary / 金库总资金指标
========================================

This data structure defines the summary fund metrics of a strategy vault.  
此数据结构定义了策略金库的资金使用、风险敞口和交易指标汇总信息。

Attributes:
-----------
    strategy_uuid (str, foreign_key): 
        Identifier of the strategy.  
        策略标识符。  
        Example: "62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"

    vault_value_usd (float): 
        Total value of the vault.  
        金库当前总价值。  
        Example: 15432.67

    total_collateral (float): 
        Total funds allocated to all open positions.  
        所有持仓的总抵押资产。  
        Example: 10000.0

    available_balance (float): 
        Remaining capital available for use.  
        可用于新开仓位的资金。  
        Example: 5432.67

    current_leverage (float): 
        Average leverage across positions.  
        当前平均杠杆。  
        Example: 7.4

    open_position_count (int): 
        Total number of open positions.  
        当前持仓数量。  
        Example: 3

    total_position_volume_usd (float): 
        Combined notional volume of all positions.  
        当前总持仓的名义交易量。  
        Example: 25000.0

    realized_pnl_usd (float): 
        Total realized PnL.  
        累计已实现盈亏。  
        Example: -125.50

    unrealized_pnl_usd (float): 
        Combined floating PnL.  
        累计未实现盈亏。  
        Example: 432.80

    cumulative_trading_volume_usd (float): 
        Total trading volume since inception.  
        策略启动以来的累计交易量。  
        Example: 245000.0

    cumulative_fees_usd (float): 
        Total fees paid.  
        累计手续费支出。  
        Example: 185.76

    updated_at (str): 
        Timestamp of last update.  
        最后更新时间。  
        Example: "2025-03-21T14:20:00Z"