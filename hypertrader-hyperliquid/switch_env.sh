# if ../../main.env exists
# move ../../.env to ../../test.env
# move ../../main.env to ../../.env
# else
# move ../../test.env to ../../.env
# move ../../.env to ../../main.env

if [ -f ../../main.env ]; then
    mv ../../.env ../../test.env
    mv ../../main.env ../../.env
    echo "Switched to mainnet"
else
    mv ../../.env ../../main.env
    mv ../../test.env ../../.env
    echo "Switched to testnet"
fi