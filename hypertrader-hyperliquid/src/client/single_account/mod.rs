use crate::models::withdraw::WithdrawConfig;
use hyperliquid_rust_sdk::Signer;
use hyperliquid_rust_sdk::{
    BaseUrl, ClientCancelRequest, ClientOrderRequest, ExchangeClient, ExchangeResponseStatus, H160,
    LocalWallet,
};
use std::str::FromStr;

use super::data::DataClient;

#[derive(Debug)]
pub struct SingleAccountClient {
    // pub config: Config,
    pub client: ExchangeClient,
    pub with_vault: bool,

    pub user_address: H160,
    pub user_wallet: LocalWallet,

    pub is_testnet: bool,
}

impl SingleAccountClient {
    pub async fn new(secret_key: &str, is_testnet: bool) -> Self {
        let wallet = LocalWallet::from_str(&secret_key).unwrap();
        let user_address = wallet.address();

        let client: ExchangeClient = ExchangeClient::new(
            None,
            wallet.clone(),
            if is_testnet {
                Some(BaseUrl::Testnet)
            } else {
                Some(BaseUrl::Mainnet)
            },
            None,
            None,
            None,
        )
        .await
        .unwrap();

        Self {
            client,
            user_address,
            user_wallet: wallet,
            with_vault: false,
            is_testnet: false,
        }
    }

    pub fn address(&self) -> String {
        format!("{:#x}", self.user_address)
    }

    // send usd in hyperliquid exchange
    pub async fn usd_send(
        &self,
        amount: &str,
        to_address: &str,
    ) -> anyhow::Result<ExchangeResponseStatus> {
        let res: ExchangeResponseStatus = self
            .client
            .usdc_transfer(amount, to_address, Some(&self.user_wallet))
            .await?;

        Ok(res)
    }

    pub async fn new_with_vault(secret_key: &str, vault_address: H160, is_testnet: bool) -> Self {
        let wallet = LocalWallet::from_str(&secret_key).unwrap();
        let user_address = wallet.address();

        let client: ExchangeClient = ExchangeClient::new(
            None,
            wallet.clone(),
            if is_testnet {
                Some(BaseUrl::Testnet)
            } else {
                Some(BaseUrl::Mainnet)
            },
            None,
            Some(vault_address),
            None,
        )
        .await
        .unwrap();

        Self {
            client,
            user_address,
            user_wallet: wallet,
            with_vault: true,
            is_testnet,
        }
    }

    pub async fn withdraw_usd_from_vault(
        &self,
        amount: u64,
        vault: String,
        nonce: u64,
    ) -> anyhow::Result<ExchangeResponseStatus> {
        let res = self
            .client
            .vault_transfer(false, amount, vault, None, nonce)
            .await
            .unwrap();

        Ok(res)
    }

    pub async fn deposite_usd_to_vault(
        &self,
        amount: u64,
        vault: String,
        nonce: u64,
    ) -> anyhow::Result<ExchangeResponseStatus> {
        let res = self
            .client
            .vault_transfer(true, amount, vault, None, nonce)
            .await
            .unwrap();

        Ok(res)
    }

    // pub async fn deposit_usd_to_vault(
    //     &self,
    //     amount: usize,
    //     vault_address: String,
    // ) -> anyhow::Result<()> {
    //     let res = self
    //         .client
    //         .vault_usd_transfer(vault_address, true, amount)
    //         .await
    //         .unwrap();

    //     tracing::info!("Vault transfer result: {res:?}");

    //     Ok(())
    // }

    // pub async fn withdraw_usd_from_vault(
    //     &self,
    //     amount: usize,
    //     vault_address: String,
    // ) -> anyhow::Result<ExchangeResponseStatus> {
    //     let res = self
    //         .client
    //         .vault_usd_transfer(vault_address, false, amount)
    //         .await
    //         .unwrap();

    //     Ok(res)
    // }

    // pub async fn withdraw_usd_from_vaults(
    //     &self,
    //     withdraw_config: WithdrawConfig,
    //     data_client: &DataClient,
    // ) -> anyhow::Result<()> {
    //     let vaults = data_client
    //         .user_vault_equities_with_details(self.user_address)
    //         .await?;

    //     for vault in vaults {
    //         let amount = withdraw_config.given_amount(vault.max_withdrawable);
    //         let res = self
    //             .client
    //             .vault_usd_transfer(vault.vault_address, false, amount)
    //             .await
    //             .unwrap();
    //         tracing::info!("Vault transfer result: {res:?}");
    //     }

    //     Ok(())
    // }

    // simple account order
    // pub async fn order(
    //     &self,
    //     coin: &str,
    //     is_buy: bool,
    //     limit_px: f64,
    //     sz: f64,
    //     order_type: ClientOrder,
    // ) -> anyhow::Result<ExchangeResponseStatus> {
    //     if self.with_vault {
    //         return Err(anyhow::anyhow!(
    //             "Vault is enabled. Please use vault_order instead."
    //         ));
    //     }

    //     let order = ClientOrderRequest {
    //         asset: coin.to_string(),
    //         is_buy,
    //         reduce_only: false,
    //         limit_px,
    //         sz,
    //         cloid: None,
    //         order_type,
    //     };

    //     let response: ExchangeResponseStatus = self
    //         .client
    //         .order(order, Some(&self.user_wallet), next_nonce())
    //         .await
    //         .unwrap();

    //     Ok(response)
    // }
    pub async fn order(
        &self,
        order: ClientOrderRequest,
        wallet: Option<&LocalWallet>,
        nonce: u64,
    ) -> anyhow::Result<ExchangeResponseStatus> {
        if self.with_vault {
            return Err(anyhow::anyhow!(
                "Vault is enabled. Please use vault_order instead."
            ));
        }

        let response: ExchangeResponseStatus =
            self.client.order(order, wallet, nonce).await.unwrap();

        Ok(response)
    }

    pub async fn vault_order(
        &self,
        order: ClientOrderRequest,
        nonce: u64,
    ) -> anyhow::Result<ExchangeResponseStatus> {
        if !self.with_vault {
            return Err(anyhow::anyhow!("Vault is not enabled"));
        }

        let response = self.client.order(order, None, nonce).await?;

        Ok(response)
    }

    pub async fn vault_order_cancel(
        &self,
        coin: &str,
        oid: u64,
    ) -> anyhow::Result<ExchangeResponseStatus> {
        let cancel = ClientCancelRequest {
            asset: coin.to_string(),
            oid,
        };

        let response: hyperliquid_rust_sdk::ExchangeResponseStatus =
            self.client.cancel(cancel, None).await.unwrap();

        Ok(response)
    }

    pub async fn convert_to_multi_sig_account(
        &self,
        authorized_users: Vec<String>,
        threshold: usize,
    ) -> anyhow::Result<ExchangeResponseStatus> {
        let res: ExchangeResponseStatus = self
            .client
            .convert_to_multi_sig_user(authorized_users, threshold, None)
            .await?;
        Ok(res)
    }

    pub async fn withdraw_from_hyperliquid_to_chain(
        &self,
        to_address: String,
        withdraw_config: WithdrawConfig,
        data_client: &DataClient,
    ) -> anyhow::Result<ExchangeResponseStatus> {
        // minimal withdraw amount is 2 usd
        // withdraw fee is 1 usd

        let res = if let Some(amount) = withdraw_config.is_partial_and_skip_check_str() {
            self.client
                .withdraw_from_bridge(amount, to_address, None)
                .await?
        } else {
            let user_data = data_client.user_data(self.user_address).await?;
            tracing::info!("withdrawable: {}", user_data.withdrawable);

            let withdraw_amount = withdraw_config.given_amount_str(user_data.withdrawable);
            self.client
                .withdraw_from_bridge(withdraw_amount, to_address, None)
                .await?
        };

        Ok(res)
    }
}
