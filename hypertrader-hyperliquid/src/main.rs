use std::str::FromStr;

use hyperliquid_rust_sdk::{
    Actions, ClientLimit, ClientOrder, ClientOrderRequest, H160, H256, LocalWallet, Signer,
    UsdSend, next_nonce, sign_hash,
};
use hypertrader_hyperliquid::{
    client::{data::DataClient, single_account::SingleAccountClient},
    models::withdraw::WithdrawConfig,
};
use hypertrader_utils::{env::GLOBAL_ENVS, init::ensure_inited};
use serde_json::json;

const TEST_USER0: &str = "******************************************"; // multi-sig address 1. vault in mainnet
const TEST_USER1: &str = "******************************************"; // main vault address, (multi-sig user in testnet)
const TEST_USER2: &str = "******************************************"; // multi-sig address 2
const TEST_USER3: &str = "******************************************"; // multi-sig address 3, destination address

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();
    // env_logger::init();

    let is_testnet = true;
    // let secret_key = GLOBAL_ENVS.secret();
    let vault_address = GLOBAL_ENVS.vault();

    let secret_key = std::env::var("TEST_USER0_SECRET").unwrap();

    // let user = H160::from_str(&std::env::var("HYPER_ADDRESS").unwrap()).unwrap();
    // ******************************************
    // let user = H160::from_str("******************************************").unwrap();

    let vault_address = H160::from_str(vault_address).unwrap();
    let (data_client, _) = DataClient::new(is_testnet).await?;
    let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let user = client.user_address;

    // println!("{:x}", message_hash);

    // multi_sig_usdc_transfer

    // tracing::info!("USD transfer result: {res:?}");

    // println!("{:?}", hex::decode("66eee").unwrap());
    // println!("{:x?}", 421614);

    // let withdraw_config = WithdrawConfig::new_all();
    // client
    //     .withdraw_usd_from_vaults(withdraw_config, &data_client)
    //     .await?;

    // let role = data_client.user_role(user).await;
    // tracing::info!("role: {role:#?}");
    // let user = H160::from_str(&std::env::var("HYPER_ADDRESS").unwrap()).unwrap();
    // let user_data = &data_client.user_data(user).await;
    // tracing::info!("user_data: {user_data:#?}");

    // // deposit 5 USD to vault
    // let usd = 5000000; // 5 USD
    // client
    //     .deposit_usd_to_vault(usd, vault_address.to_string())
    //     .await
    //     .unwrap();

    // // withdraw 1 USD from vault
    // let usd = 1000000; // 1 USD
    // client
    //     .withdraw_usd_from_vault(usd, vault_address.to_string())
    //     .await
    //     .unwrap();

    // let client =
    //     SingleAccountClient::new_with_vault(secret_key, vault_address.parse().unwrap(), is_testnet)
    //         .await;

    // // get open orders
    // let orders = data_client
    //     .open_orders(vault_address.parse().unwrap())
    //     .await?;

    // // get detailed open orders
    // let order_statuses = data_client
    //     .open_orders_detailed(vault_address.parse().unwrap())
    //     .await?;
    // tracing::info!("order_statuses: {order_statuses:#?}");

    // // cancel order
    // let response = client.vault_order_cancel("XRP", ***********).await.unwrap();
    // tracing::info!("cancel response: {response:#?}");

    Ok(())
}
