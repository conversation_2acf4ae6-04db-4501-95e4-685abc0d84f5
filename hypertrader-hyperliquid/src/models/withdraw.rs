#[derive(Default)]
pub enum WithdrawConfig {
    // 全部提现
    #[default]
    All,
    // 部分提现
    Partial {
        amount: usize,    // 提现金额(1e6 * usd)
        skip_check: bool, // 是否跳过检查
    },
    // 比例
    Ratio {
        ratio: f64, // 0-1
    },
}

impl WithdrawConfig {
    pub fn new_all() -> Self {
        Self::All
    }

    pub fn new_partial(amount: f64, skip_check: bool) -> Self {
        Self::Partial {
            amount: (amount * 1e6).floor() as usize,
            skip_check,
        }
    }

    pub fn new_ratio(ratio: f64) -> Self {
        assert!(
            ratio >= 0.0 && ratio <= 1.0,
            "ratio must be between 0 and 1"
        );
        Self::Ratio { ratio }
    }

    pub fn is_partial_and_skip_check(&self) -> Option<usize> {
        match self {
            Self::Partial { amount, skip_check } if *skip_check => Some(*amount),
            _ => None,
        }
    }

    pub fn is_partial_and_skip_check_str(&self) -> Option<String> {
        match self {
            Self::Partial { amount, skip_check } if *skip_check => {
                Some((*amount as f64 / 1e6).to_string())
            }
            _ => None,
        }
    }

    // amount state
    pub fn given_amount(&self, amount: f64) -> usize {
        let given_amount = (amount * 1e6).floor() as usize;

        match self {
            Self::All => given_amount,
            Self::Partial { amount, skip_check } => {
                if *skip_check {
                    *amount
                } else {
                    if given_amount > *amount {
                        *amount
                    } else {
                        given_amount
                    }
                }
            }
            Self::Ratio { ratio } => (amount * ratio * 1e6).floor() as usize,
        }
    }

    pub fn given_amount_str(&self, amount: f64) -> String {
        let given_amount = self.given_amount(amount) as f64 / 1e6;
        format!("{}", given_amount)
    }
}
