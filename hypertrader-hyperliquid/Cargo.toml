[package]
name = "hypertrader-hyperliquid"
version = "0.1.0"
edition = "2024"

[dependencies]
hypertrader-utils = { workspace = true }
hyperliquid_rust_sdk = { workspace = true }
hypertrader-models = { workspace = true }

futures = { workspace = true }

tokio = { workspace = true, features = ["full"] }

reqwest = { workspace = true, features = ["json"] }
anyhow = { workspace = true }
tracing = { workspace = true }
env_logger = "0.11.7"

serde_json = { workspace = true, features = ["preserve_order"] }
hex = "0.4.3"
