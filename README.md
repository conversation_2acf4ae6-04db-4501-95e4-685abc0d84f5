# HyperTrader

HyperTrader is a trading system for cryptocurrency markets with a focus on high performance and reliability. This project provides WebSocket services for real-time market data and trading operations.

## Project Overview

HyperTrader connects to the Hyperliquid protocol and provides a WebSocket API for clients to subscribe to market data, submit orders, and receive notifications.

### Examples

Examples are in `hypertrader-examples` directory.

### Key Features

- Real-time WebSocket API for market data and trading operations
- Environment variable management for configuration
- Ping/pong mechanism for connection health monitoring
- Notification and greeting services
- Connection management with auto-reconnection

## Project Structure

- `src/handlers/ws/` - WebSocket handlers and connection management
  - `client.rs` - Client connection handling
  - `manager.rs` - Connection manager for all connected clients
  - `greeting/` - Simple greeting service example
  - `notifier/` - Notification service for updates
  - `types.rs` - Message types and constants

## Getting Started

### Prerequisites

- Rust 1.70.0 or later
- Access to Hyperliquid API endpoints

### Environment Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/hypertrader.git
   cd hypertrader
   ```

2. Create a `.env` file in the project root based on the `.env.example` file:
   ```bash
   cp .env.example .env
   ```

3. Edit the `.env` file to configure your environment:
   ```
   HYPER_SECRET=your_api_secret_key
   HYPER_VAULT=your_vault_address
   ENV=development  # or production
   ```



### Running the Application

1. Run the server:
   ```bash
   cargo run
   ```

The WebSocket server will start and listen on port 5800 by default.

## Using the WebSocket API

Connect to the WebSocket endpoint:
```
ws://localhost:5800/ws
```

### Message Types

The WebSocket API uses JSON messages with the following structure:

```json
{
  "type": "MessageType",
  "data": {
    // Message-specific data
  }
}
```

### Common Message Types

- `Connect` - Initial connection message
- `Subscribe` - Subscribe to a channel or symbol
- `Unsubscribe` - Unsubscribe from a channel or symbol
- `Ping`/`Pong` - Connection health check
- `Custom` - Custom events like greetings or notifications

### Example: Simple Greeting

Client sends:
```json
{
  "type": "Custom",
  "data": {
    "event": "greeting_request",
    "data": {
      "name": "John"
    }
  }
}
```

Server responds:
```json
{
  "type": "Custom",
  "data": {
    "event": "greeting_response",
    "data": {
      "greeting": {
        "id": "uuid",
        "name": "John",
        "message": "Hello, John!",
        "timestamp": "2023-06-19T10:30:00Z"
      }
    }
  }
}
```

### Connection Health Monitoring

The server will send ping messages every 30 seconds. Clients should respond with pong messages to keep the connection alive.

## Development

### Testing

Run the test suite:
```bash
cargo test
```

### Adding New Environment Variables

1. Add new fields to the `GlobalEnvsInner` struct in `src/utils/env/mod.rs`
2. Add initialization in the `new()` method
3. Add accessor methods in the `GlobalEnvs` implementation

### Using Environment Variables in Your Code

```rust
use crate::utils::env::GLOBAL_ENVS;

fn my_function() {
    // Initialize environment (typically done at application startup)
    GLOBAL_ENVS.init();

    // Access variables
    let secret = GLOBAL_ENVS.secret();
    let vault = GLOBAL_ENVS.vault();

    // Check environment
    if GLOBAL_ENVS.is_production() {
        // Production-specific logic
    }

    // Get custom variables
    let custom_var = GLOBAL_ENVS.get_env_or("CUSTOM_VAR", "default_value");
}
```

### Error Handling
Current error handling is based on the `Result` type and `anyhow` crate.

Later adjust error handling to use `thiserror` crate, and define custom error types.

### Logging
Current logging is based on the `tracing` crate.

We already have a logging setup in `src/utils/logger`.


### Environment Variables
We already have a logging setup in `src/utils/env` using `dotenvy` crate.


### Init
For global init, we have `src/init.rs` using `static_init` crate to initialize global variables before main function.
Also, it can initialize event before testings.

### Async
We use `tokio` crate for async programming.

Use `dashmap` instead of `std::collections::HashMap` for concurrent map data structures.


### database