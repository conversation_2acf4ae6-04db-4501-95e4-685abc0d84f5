# Start with a Rust base image
FROM rust:1.85.1-slim-bullseye AS builder

# Install dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    pkg-config \
    libssl-dev \
    build-essential \
    git \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create a new empty project
WORKDIR /app

# Copy over manifests and lock files
COPY Cargo.toml Cargo.lock ./
COPY rust-toolchain ./

# Copy all crates
COPY . .

# Build dependencies - this creates a cached layer for dependencies
# RUN mkdir -p src && \
#     cargo build --release

# Copy actual source code
# COPY src ./src

# Build the application
RUN cargo build --release --workspace --target-dir /app/target

# Create a smaller runtime image
FROM debian:bullseye-slim AS runtime

# Install runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    ca-certificates \
    libssl1.1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy the built executables from the builder stage
COPY --from=builder /app/target/release/range_updater /app/range_updater
COPY --from=builder /app/target/release/hypertrader /app/hypertrader

# Create environment directory
RUN mkdir -p /app/env

# Copy all environment files
COPY env/*.env ./env/

# Expose the WebSocket port
EXPOSE 5800
EXPOSE 9999

# Copy and set permissions for the startup script
COPY start.sh /app/start.sh
RUN chmod +x /app/start.sh
CMD ["/app/start.sh"]