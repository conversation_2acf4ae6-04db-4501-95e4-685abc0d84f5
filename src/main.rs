use hypertrader::router::router;
use hypertrader_utils::init::ensure_inited;
use salvo::prelude::*;
use tracing::info;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    // // 创建 InfoClient 用于 WebSocket 连接
    // let mut info_client = InfoClient::with_reconnect(None, Some(BaseUrl::Mainnet)).await?;

    // let meta = info_client.meta().await.unwrap();
    // println!("meta: {:#?}", meta);

    // // 创建接收通道
    // let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel::<Message>();

    // // 订阅用户事件和通知
    // // let user_sub = Subscription::UserEvents {
    // //     user: wallet_address,
    // // };
    // // info_client.subscribe(user_sub, tx.clone()).await?;

    // let notification_sub = Subscription::Notification {
    //     user: wallet_address,
    // };
    // info_client.subscribe(notification_sub, tx.clone()).await?;

    // let vault_sub = Subscription::UserEvents {
    //     user: vault_address,
    // };
    // info_client.subscribe(vault_sub, tx.clone()).await?;

    // // 处理 WebSocket 消息
    // tokio::spawn(async move {
    //     while let Some(msg) = rx.recv().await {
    //         match msg {
    //             Message::User(user_data) => {
    //                 println!("收到用户数据: {:?}", user_data);
    //             }
    //             Message::Notification(notification) => {
    //                 println!("收到通知: {:?}", notification);
    //             }
    //             _ => {}
    //         }
    //     }
    // });

    // let usd = "1"; // 1 USD
    // let is_deposit = true;

    // let res = exchange_client
    //     .vault_transfer(is_deposit, usd.to_string(), Some(vault_address), None)
    //     .await
    //     .unwrap();
    // info!("Vault transfer result: {res:?}");

    // // // 执行 vault 存款
    // // let deposit_result = exchange_client
    // //     .vault_transfer(true, "5.01".to_string(), None, None)
    // //     .await?;

    // // println!("存款结果: {:?}", deposit_result);

    // // 等待一段时间观察 WebSocket 事件
    // tokio::time::sleep(tokio::time::Duration::from_secs(230)).await;

    // =======================

    // // 创建ExchangeClient
    // let exchange_client = ExchangeClient::new(
    //     None,
    //     wallet.clone(),
    //     Some(BaseUrl::Mainnet),
    //     None,
    //     Some(vault_address),
    // )
    // .await?;

    // // 创建WebSocket连接
    // let ws_url = "wss://api.hyperliquid.xyz/ws";
    // let mut ws_manager = WsManager::new(ws_url.to_string(), true).await?;

    // // 创建接收通道用于接收WebSocket消息
    // let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel::<Message>();

    // // 订阅用户事件
    // // let user_sub = Subscription::UserEvents {
    // //     user: wallet_address,
    // // };
    // // let user_sub_id = ws_manager.subscribe(user_sub, tx.clone()).await?;

    // let notification_sub = Subscription::Notification {
    //     user: wallet_address,
    // };
    // let identifier = serde_json::to_string(&notification_sub)
    //     .map_err(|e| HyperError::JsonParse(e.to_string()))?;
    // ws_manager.add_subscription(identifier, tx).await?;

    // 假设您想订阅特定用户的消息和通知

    // let info_client = InfoClient::new(None, Some(BaseUrl::Mainnet)).await.unwrap();
    // // let meta = info_client.meta_and_asset_contexts().await.unwrap();
    // let meta = info_client.meta().await.unwrap();
    // println!("meta: {:#?}", meta);

    // 初始化WebSocket管理器（如果需要）
    // 启动salvo服务器
    let router = router().await;
    let port = std::env::var("SIMU_TRACING_API_LISTEN_PORT").unwrap_or("9999".to_string());
    let acceptor = TcpListener::new(format!("0.0.0.0:{}", port)).bind().await;

    info!("WebSocket start, listen port: {}", port);

    Server::new(acceptor).serve(router).await;

    Ok(())
}
