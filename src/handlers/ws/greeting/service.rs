use chrono::Utc;
use salvo::websocket::Message;
use tokio::sync::mpsc::UnboundedSender;
use tracing::info;
use uuid::Uuid;

use crate::handlers::ws::greeting::models::Greeting;
use crate::handlers::ws::types::WsMessage;

// 简单的问候服务 - 提供基础的问候功能
pub struct GreetingService {}

impl GreetingService {
    // 创建新的问候服务实例
    pub fn new() -> Self {
        Self {}
    }

    // 生成问候消息
    pub fn greet(
        &self,
        name: String,
    ) -> Result<Greeting, Box<dyn std::error::Error + Send + Sync>> {
        // 创建问候
        let greeting = Greeting {
            id: Uuid::new_v4().to_string(),
            name: name.clone(),
            message: format!("你好，{}！", name),
            timestamp: Utc::now().to_rfc3339(),
        };

        Ok(greeting)
    }

    // 发送问候消息到客户端
    pub fn send_greeting(
        &self,
        greeting: &Greeting,
        tx: &UnboundedSender<Message>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 创建推送消息
        let push_msg = WsMessage::Custom {
            event: "greeting".to_string(),
            data: serde_json::to_value(greeting)?,
        };

        // 序列化并发送
        let json = serde_json::to_string(&push_msg)?;
        tx.send(Message::text(json))?;

        info!("问候已发送给 {}: {}", greeting.name, greeting.message);
        Ok(())
    }
}
