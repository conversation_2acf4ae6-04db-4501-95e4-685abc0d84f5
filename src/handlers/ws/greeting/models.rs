use serde::{Deserialize, Serialize};

// 简单的问候消息类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Greeting {
    pub id: String,        // 唯一标识符
    pub name: String,      // 用户名
    pub message: String,   // 问候消息内容
    pub timestamp: String, // 时间戳
}

// 问候消息请求
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GreetingRequest {
    pub name: String, // 用户名
}

// 问候消息响应
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GreetingResponse {
    pub greeting: Greeting, // 问候消息
}
