use salvo::websocket::Message;
use tokio::sync::mpsc::UnboundedSender;
use tracing::{debug, error};

use crate::handlers::ws::greeting::GREETING_SERVICE;
use crate::handlers::ws::greeting::models::{GreetingRequest, GreetingResponse};
use crate::handlers::ws::types::WsMessage;

// 处理问候请求
pub async fn handle_greeting_request(
    client_id: &str,
    tx: &UnboundedSender<Message>,
    data: serde_json::Value,
) {
    match serde_json::from_value::<GreetingRequest>(data) {
        Ok(request) => {
            debug!("收到来自 {} 的问候请求: {}", client_id, request.name);

            // 处理问候请求
            match GREETING_SERVICE.greet(request.name) {
                Ok(greeting) => {
                    // 发送问候响应
                    let response = GreetingResponse { greeting };
                    if let Ok(json) = serde_json::to_string(&WsMessage::Custom {
                        event: "greeting_response".to_string(),
                        data: serde_json::to_value(response).unwrap_or_default(),
                    }) {
                        if let Err(e) = tx.send(Message::text(json)) {
                            error!("发送问候响应失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    send_error(tx, 500, format!("生成问候失败: {}", e));
                }
            }
        }
        Err(e) => {
            send_error(tx, 400, format!("无效的问候请求格式: {}", e));
        }
    }
}

// 发送错误响应
fn send_error(tx: &UnboundedSender<Message>, code: i32, message: String) {
    if let Ok(error_json) = serde_json::to_string(&WsMessage::Error { code, message }) {
        let _ = tx.send(Message::text(error_json));
    }
}
