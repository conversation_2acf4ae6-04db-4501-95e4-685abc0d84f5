use serde::{Deserialize, Serialize};

// 重连配置
pub const RECONNECT_INTERVAL_MS: u64 = 2000; // 初始重连间隔
pub const MAX_RECONNECT_INTERVAL_MS: u64 = 30000; // 最大重连间隔
pub const RECONNECT_FACTOR: f64 = 1.5; // 重连间隔增长因子
pub const MAX_RECONNECT_ATTEMPTS: usize = 10; // 最大重连尝试次数

// 心跳配置
pub const HEARTBEAT_INTERVAL_MS: u64 = 30000; // 心跳间隔
pub const PING_TIMEOUT_MS: u64 = 5000; // ping超时时间

// WebSocket消息类型
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(tag = "type", content = "data")]
pub enum WsMessage {
    Connect {
        client_id: String,
    },
    Subscribe {
        channel: String,
        symbols: Vec<String>,
    },
    Unsubscribe {
        channel: String,
        symbols: Vec<String>,
    },
    MarketData {
        symbol: String,
        price: f64,
        volume: f64,
    },
    OrderUpdate {
        order_id: String,
        status: String,
    },
    Error {
        code: i32,
        message: String,
    },
    Ping,
    Pong,
    Close {
        reason: String,
    },
    // 自定义消息类型 - 用于扩展不同业务模块
    Custom {
        event: String,
        data: serde_json::Value,
    },
}
