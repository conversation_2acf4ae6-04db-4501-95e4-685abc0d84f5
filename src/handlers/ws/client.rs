use std::time::Instant;

use anyhow::Result;
use salvo::websocket::Message;
use tokio::sync::mpsc::UnboundedSender;

use crate::handlers::ws::types::WsMessage;

// WebSocket客户端状态
#[derive(Clone)]
pub struct WsClient {
    pub id: String,
    pub sender: UnboundedSender<Message>,
    pub last_ping: Instant,
    pub subscriptions: Vec<String>,
}

impl WsClient {
    pub fn new(id: String, sender: UnboundedSender<Message>) -> Self {
        Self {
            id,
            sender,
            last_ping: Instant::now(),
            subscriptions: Vec::new(),
        }
    }

    pub fn send_message(&self, msg: WsMessage) -> Result<()> {
        let json = serde_json::to_string(&msg)?;
        self.sender.send(Message::text(json))?;
        Ok(())
    }

    pub fn add_subscription(&mut self, channel: String) {
        if !self.subscriptions.contains(&channel) {
            self.subscriptions.push(channel);
        }
    }

    pub fn remove_subscription(&mut self, channel: &str) {
        self.subscriptions.retain(|c| c != channel);
    }
}
