use std::time::{Duration, Instant};

use futures_util::StreamExt;
use salvo::{
    Error as SalvoError,
    websocket::{Message, WebSocket},
};
use tokio::sync::mpsc::{self, UnboundedSender};
use tokio_stream::wrappers::UnboundedReceiverStream;
use tracing::{debug, error, info, warn};

use crate::handlers::ws::{
    NOTIFICATION_SERVICE,
    greeting::handle_greeting_request,
    manager::WS_MANAGER,
    notifier::types::NotificationQuery,
    types::{
        HEARTBEAT_INTERVAL_MS, MAX_RECONNECT_ATTEMPTS, MAX_RECONNECT_INTERVAL_MS, PING_TIMEOUT_MS,
        RECONNECT_FACTOR, RECONNECT_INTERVAL_MS, WsMessage,
    },
};

// Handle WebSocket connection
pub async fn handle_socket(ws: WebSocket, client_id: String) {
    info!("New WebSocket connection: {}", client_id);

    // Split sender and receiver channels
    let (ws_sender, mut ws_receiver) = ws.split();

    // Create message channel
    let (tx, rx) = mpsc::unbounded_channel();
    let rx = UnboundedReceiverStream::new(rx);

    // Register client
    WS_MANAGER.register_client(client_id.clone(), tx.clone());

    // Start message sending task
    tokio::spawn(async move {
        if let Err(e) = rx.map(|m| Ok::<_, SalvoError>(m)).forward(ws_sender).await {
            error!("WebSocket send error: {}", e);
        }
    });

    // Send welcome message
    let connect_msg = WsMessage::Connect {
        client_id: client_id.clone(),
    };
    if let Ok(json) = serde_json::to_string(&connect_msg) {
        let _ = tx.send(Message::text(json));
    }

    // Handle received messages
    let mut reconnect_interval = RECONNECT_INTERVAL_MS;
    let mut reconnect_attempts = 0;
    let mut last_ping_sent = Instant::now();
    let mut last_activity = Instant::now(); // Track last activity time
    let mut ping_in_progress = false;

    'connection: loop {
        // Add timeout mechanism to detect disconnections
        match tokio::time::timeout(
            Duration::from_millis(HEARTBEAT_INTERVAL_MS * 2),
            ws_receiver.next(),
        )
        .await
        {
            // Message received
            Ok(Some(Ok(msg))) => {
                // Reset reconnection parameters
                reconnect_interval = RECONNECT_INTERVAL_MS;
                reconnect_attempts = 0;
                last_activity = Instant::now(); // Update last activity time

                match msg {
                    msg if msg.is_text() => {
                        if let Ok(text) = msg.as_str() {
                            process_text_message(&client_id, &tx, text.to_owned()).await;
                        }
                    }
                    msg if msg.is_binary() => {
                        debug!("Received binary message: {} bytes", msg.as_bytes().len());
                    }
                    msg if msg.is_ping() => {
                        // Respond to ping
                        let ping_data = msg.as_bytes().to_vec();
                        if tx.send(Message::pong(ping_data)).is_err() {
                            break 'connection;
                        }
                    }
                    msg if msg.is_pong() => {
                        // Received pong message, immediately reset ping status
                        ping_in_progress = false;
                        if let Some(mut client) = WS_MANAGER.clients.get_mut(&client_id) {
                            client.last_ping = Instant::now();
                            debug!("Client {} heartbeat updated", client_id);
                        }
                    }
                    msg if msg.is_close() => {
                        info!("Client {} requested connection close", client_id);
                        break 'connection;
                    }
                    _ => {}
                }

                // After receiving and successfully processing any message, check if ping needs to be sent
                let now = Instant::now();
                if !ping_in_progress
                    && now.duration_since(last_ping_sent).as_millis()
                        > HEARTBEAT_INTERVAL_MS as u128
                {
                    if let Ok(ping_json) = serde_json::to_string(&WsMessage::Ping) {
                        if tx.send(Message::text(ping_json)).is_ok() {
                            last_ping_sent = now;
                            ping_in_progress = true;
                            debug!("Sent heartbeat to client: {}", client_id);
                        }
                    }
                }
            }
            // Timeout or error
            _ => {
                warn!("WebSocket connection interrupted or timed out: {}", client_id);

                // Only attempt to detect timeout if ping is in progress and timed out
                let now = Instant::now();
                if ping_in_progress
                    && now.duration_since(last_ping_sent).as_millis() > PING_TIMEOUT_MS as u128
                {
                    warn!("Client {} ping timeout", client_id);
                    ping_in_progress = false; // Reset ping status

                    // Only close connection if there's been no activity for a long time
                    if now.duration_since(last_activity).as_millis()
                        > (HEARTBEAT_INTERVAL_MS * 3) as u128
                    {
                        error!("Client {} no response for extended period, closing connection", client_id);
                        break 'connection;
                    }
                }

                if reconnect_attempts >= MAX_RECONNECT_ATTEMPTS {
                    error!("Maximum reconnection attempts reached, giving up: {}", client_id);
                    break 'connection;
                }

                // Exponential backoff reconnection
                reconnect_attempts += 1;
                info!(
                    "Attempting reconnection ({}/{}), waiting {}ms: {}",
                    reconnect_attempts, MAX_RECONNECT_ATTEMPTS, reconnect_interval, client_id
                );

                tokio::time::sleep(Duration::from_millis(reconnect_interval)).await;

                // Increase reconnection interval (exponential backoff)
                reconnect_interval = (reconnect_interval as f64 * RECONNECT_FACTOR) as u64;
                if reconnect_interval > MAX_RECONNECT_INTERVAL_MS {
                    reconnect_interval = MAX_RECONNECT_INTERVAL_MS;
                }

                continue 'connection;
            }
        }
    }

    // Clean up resources
    info!("WebSocket connection closed: {}", client_id);
    WS_MANAGER.remove_client(&client_id);
}

// Process text messages
pub async fn process_text_message(client_id: &str, tx: &UnboundedSender<Message>, text: String) {
    // Parse message
    match serde_json::from_str::<WsMessage>(&text) {
        Ok(ws_msg) => {
            debug!("Received message: {:?}", ws_msg);

            match ws_msg {
                WsMessage::Ping => {
                    // Respond to ping, only send pong response
                    if let Ok(pong_json) = serde_json::to_string(&WsMessage::Pong) {
                        if let Err(e) = tx.send(Message::text(pong_json)) {
                            error!("Cannot send pong response: {}", e);
                            return;
                        }
                    }

                    // Update client heartbeat time without affecting other states
                    if let Some(mut client) = WS_MANAGER.clients.get_mut(client_id) {
                        client.last_ping = Instant::now();
                        debug!("Responded to client {} ping request", client_id);
                    }
                }
                WsMessage::Subscribe { channel, symbols } => {
                    info!("Client {} subscribing: {} - {:?}", client_id, channel, symbols);

                    // Handle subscription
                    if let Some(mut client) = WS_MANAGER.clients.get_mut(client_id) {
                        client.add_subscription(channel.clone());

                        // Actual subscription business logic can be added here
                    }

                    // Respond with subscription confirmation
                    let response = WsMessage::Subscribe {
                        channel,
                        symbols: symbols.clone(),
                    };
                    if let Ok(json) = serde_json::to_string(&response) {
                        let _ = tx.send(Message::text(json));
                    }
                }
                WsMessage::Unsubscribe {
                    channel,
                    symbols: _,
                } => {
                    info!("Client {} unsubscribing: {}", client_id, channel);

                    // Handle unsubscription
                    if let Some(mut client) = WS_MANAGER.clients.get_mut(client_id) {
                        client.remove_subscription(&channel);

                        // Actual unsubscription business logic can be added here
                    }
                }
                WsMessage::Close { reason } => {
                    info!("Client {} requested close: {}", client_id, reason);
                    // Server can actively close the connection
                    // Client will detect closure in the next receive loop
                }
                // Handle notification-related messages
                WsMessage::Custom { event, data } => match event.as_str() {
                    "greeting_request" => {
                        // Handle greeting request
                        handle_greeting_request(client_id, tx, data).await;
                    }
                    "get_notifications" => {
                        if let Ok(query) = serde_json::from_value::<NotificationQuery>(data) {
                            handle_get_notifications(client_id, tx, query).await;
                        } else {
                            send_error(tx, 400, "Invalid notification query parameters".to_string());
                        }
                    }
                    "mark_notification_read" => {
                        if let Some(notification_id) =
                            data.get("notification_id").and_then(|v| v.as_str())
                        {
                            handle_mark_notification_read(client_id, tx, notification_id).await;
                        } else {
                            send_error(tx, 400, "Missing notification ID".to_string());
                        }
                    }
                    _ => {
                        debug!("Unhandled custom event: {}", event);
                    }
                },
                // Handle other message types...
                _ => {
                    debug!("Unhandled message type: {:?}", ws_msg);
                }
            }
        }
        Err(e) => {
            warn!("Cannot parse message: {} - {}", text, e);

            // Send error response
            send_error(tx, 400, format!("Invalid message format: {}", e));
        }
    }
}

// Handle get notifications request
async fn handle_get_notifications(
    _client_id: &str,
    tx: &UnboundedSender<Message>,
    query: NotificationQuery,
) {
    // Get notifications
    let notifications = NOTIFICATION_SERVICE.get_notifications(query).await;

    // Send response
    let response = WsMessage::Custom {
        event: "notifications".to_string(),
        data: serde_json::json!({
            "notifications": notifications,
            "total": notifications.len(),
        }),
    };

    if let Ok(json) = serde_json::to_string(&response) {
        let _ = tx.send(Message::text(json));
    }
}

// Handle marking notification as read
async fn handle_mark_notification_read(
    client_id: &str,
    tx: &UnboundedSender<Message>,
    notification_id: &str,
) {
    match NOTIFICATION_SERVICE
        .mark_as_read(client_id, notification_id)
        .await
    {
        Ok(_) => {
            // Send success response
            let response = WsMessage::Custom {
                event: "notification_marked_read".to_string(),
                data: serde_json::json!({
                    "notification_id": notification_id,
                    "success": true,
                }),
            };

            if let Ok(json) = serde_json::to_string(&response) {
                let _ = tx.send(Message::text(json));
            }
        }
        Err(e) => {
            send_error(tx, 404, format!("Failed to mark notification as read: {}", e));
        }
    }
}

// Helper function to send error messages
fn send_error(tx: &UnboundedSender<Message>, code: i32, message: String) {
    let error_msg = WsMessage::Error { code, message };

    if let Ok(json) = serde_json::to_string(&error_msg) {
        let _ = tx.send(Message::text(json));
    }
}
