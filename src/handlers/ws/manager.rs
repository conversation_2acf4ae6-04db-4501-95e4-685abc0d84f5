use std::time::{Duration, Instant};

use anyhow::Result;
use salvo::websocket::Message;
use tokio::sync::mpsc::UnboundedSender;
use tracing::{debug, error, info, warn};

use crate::handlers::ws::{
    client::WsClient,
    types::{HEARTBEAT_INTERVAL_MS, WsMessage},
};

// WebSocket连接管理器
#[derive(Clone)]
pub struct WsManager {
    pub clients: dashmap::DashMap<String, WsClient>,
}

impl WsManager {
    pub fn new() -> Self {
        let manager = Self {
            clients: dashmap::DashMap::new(),
        };

        // 启动心跳检测
        let mgr_clone = manager.clone();
        tokio::spawn(async move {
            loop {
                mgr_clone.check_heartbeats().await;
                tokio::time::sleep(Duration::from_millis(HEARTBEAT_INTERVAL_MS / 2)).await;
            }
        });

        manager
    }

    pub fn register_client(&self, id: String, sender: UnboundedSender<Message>) {
        let client = WsClient::new(id.clone(), sender);
        self.clients.insert(id, client);
    }

    pub fn remove_client(&self, id: &str) {
        self.clients.remove(id);
    }

    pub fn get_client(&self, id: &str) -> Option<dashmap::mapref::one::Ref<String, WsClient>> {
        self.clients.get(id)
    }

    pub fn broadcast(&self, msg: WsMessage, except_id: Option<&str>) -> Result<()> {
        let json = serde_json::to_string(&msg)?;

        for client in self.clients.iter() {
            if let Some(except) = except_id {
                if client.key() == except {
                    continue;
                }
            }

            if let Err(e) = client.sender.send(Message::text(json.clone())) {
                error!("广播消息失败: {}", e);
            }
        }

        Ok(())
    }

    pub async fn check_heartbeats(&self) {
        let now = Instant::now();
        let mut to_remove = Vec::new();

        for client in self.clients.iter() {
            // 检查心跳超时
            if now.duration_since(client.last_ping).as_millis()
                > (HEARTBEAT_INTERVAL_MS * 2) as u128
            {
                warn!("客户端 {} 心跳超时，准备断开连接", client.id);
                to_remove.push(client.id.clone());
                continue;
            }

            // 发送ping并检查pong响应超时
            let client_id = client.id.clone();
            let client_sender = client.sender.clone();

            // 发送ping消息
            if let Ok(ping_json) = serde_json::to_string(&WsMessage::Ping) {
                // let ping_time = Instant::now();

                // 记录上次ping时间，用于检测pong响应超时
                if let Some(client) = self.clients.get_mut(&client_id) {
                    // 只有在距离上次ping时间超过心跳间隔时才发送新的ping
                    if now.duration_since(client.last_ping).as_millis()
                        > HEARTBEAT_INTERVAL_MS as u128
                    {
                        debug!("发送心跳检测到客户端: {}", client_id);
                        if let Err(e) = client_sender.send(Message::text(ping_json)) {
                            error!("发送ping失败: {} - {}", client_id, e);
                            to_remove.push(client_id.clone());
                        }
                    }
                }
            }
        }

        for id in to_remove {
            self.remove_client(&id);
            info!("已断开超时客户端: {}", id);
        }
    }
}

// 全局WebSocket管理器
lazy_static::lazy_static! {
    pub static ref WS_MANAGER: WsManager = WsManager::new();
}
