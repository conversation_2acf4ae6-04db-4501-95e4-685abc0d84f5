use super::*;

use salvo::{
    Request, Response,
    prelude::{StatusError, WebSocketUpgrade},
};

pub mod client;
pub mod greeting;
pub mod handlers;
pub mod manager;
pub mod notifier;
pub mod types;

// 重新导出关键类型和函数
pub use client::WsClient;
pub use greeting::GREETING_SERVICE;
pub use handlers::{handle_socket, process_text_message};
pub use manager::{WS_MANAGER, WsManager};
pub use notifier::NotificationService;
pub use types::WsMessage;

// 创建全局策略存储
lazy_static::lazy_static! {
    pub static ref NOTIFICATION_SERVICE: notifier::NotificationService = notifier::NotificationService::new();
}

// WebSocket入口处理函数
#[handler]
pub async fn ws_handler(req: &mut Request, res: &mut Response) -> Result<(), StatusError> {
    let client_id = req
        .query::<String>("client_id")
        .unwrap_or_else(|| uuid::Uuid::new_v4().to_string());

    WebSocketUpgrade::new()
        .upgrade(req, res, move |ws| handle_socket(ws, client_id.clone()))
        .await
}
