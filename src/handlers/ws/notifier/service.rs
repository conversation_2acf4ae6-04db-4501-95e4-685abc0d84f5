use chrono::Utc;
use salvo::websocket::Message;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, mpsc::UnboundedSender};
use uuid::Uuid;

use crate::handlers::ws::notifier::types::*;
use crate::handlers::ws::types::WsMessage;

// 通知服务 - 管理用户通知
pub struct NotificationService {
    notifications: Arc<Mutex<HashMap<String, Vec<Notification>>>>,
}

impl NotificationService {
    // 创建新的通知服务实例
    pub fn new() -> Self {
        Self {
            notifications: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    // 发送通知给指定用户
    pub async fn send_notification(
        &self,
        user_id: &str,
        title: String,
        content: String,
        priority: NotificationPriority,
        tx: Option<&UnboundedSender<Message>>,
    ) -> Result<Notification, Box<dyn std::error::Error + Send + Sync>> {
        // 创建通知
        let notification = Notification {
            id: Uuid::new_v4().to_string(),
            title,
            content,
            priority,
            created_at: Utc::now().to_rfc3339(),
            read: false,
        };

        // 存储通知
        {
            let mut notifications = self.notifications.lock().await;
            notifications
                .entry(user_id.to_string())
                .or_insert_with(Vec::new)
                .push(notification.clone());
        }

        // 如果提供了发送器，则立即推送通知
        if let Some(tx) = tx {
            self.push_notification_to_client(user_id, &notification, tx)?;
        }

        Ok(notification)
    }

    // 获取用户的通知
    pub async fn get_notifications(&self, query: NotificationQuery) -> Vec<Notification> {
        let notifications = self.notifications.lock().await;

        // 获取用户的所有通知或空列表
        let user_notifications = match notifications.get(&query.user_id) {
            Some(notes) => notes,
            None => return Vec::new(),
        };

        // 过滤通知
        let mut filtered: Vec<Notification> = user_notifications
            .iter()
            .filter(|n| {
                // 按时间过滤
                if let Some(since) = &query.since {
                    if n.created_at < *since {
                        return false;
                    }
                }

                // 按优先级过滤
                if let Some(priority) = &query.priority {
                    if &n.priority != priority {
                        return false;
                    }
                }

                true
            })
            .cloned()
            .collect();

        // 按时间倒序排序（最新的先显示）
        filtered.sort_by(|a, b| b.created_at.cmp(&a.created_at));

        // 限制结果数量
        if let Some(limit) = query.limit {
            if filtered.len() > limit as usize {
                filtered.truncate(limit as usize);
            }
        }

        filtered
    }

    // 标记通知为已读
    pub async fn mark_as_read(
        &self,
        user_id: &str,
        notification_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut notifications = self.notifications.lock().await;

        // 查找用户的通知列表
        if let Some(user_notifications) = notifications.get_mut(user_id) {
            // 查找并标记指定ID的通知
            for note in user_notifications.iter_mut() {
                if note.id == notification_id {
                    note.read = true;
                    return Ok(());
                }
            }
        }

        Err("通知未找到".into())
    }

    // 推送通知到客户端
    fn push_notification_to_client(
        &self,
        user_id: &str,
        notification: &Notification,
        tx: &UnboundedSender<Message>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 创建推送消息
        let push_msg = WsMessage::Custom {
            event: "notification".to_string(),
            data: serde_json::to_value(notification)?,
        };

        // 序列化并发送
        let json = serde_json::to_string(&push_msg)?;
        tx.send(Message::text(json))?;

        tracing::info!("通知已推送给用户 {}: {}", user_id, notification.title);
        Ok(())
    }
}
