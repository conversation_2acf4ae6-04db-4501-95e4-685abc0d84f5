use serde::{Deserialize, Serialize};

// 通知优先级
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum NotificationPriority {
    Low,
    Normal,
    High,
    Urgent,
}

// 通知类型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Notification {
    pub id: String,
    pub title: String,
    pub content: String,
    pub priority: NotificationPriority,
    pub created_at: String,
    pub read: bool,
}

// 通知查询参数
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NotificationQuery {
    pub user_id: String,
    pub since: Option<String>,
    pub priority: Option<NotificationPriority>,
    pub limit: Option<u32>,
}
