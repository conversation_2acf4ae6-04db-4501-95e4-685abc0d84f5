use hypertrader_data::cex::hyperliquid::HyperliquidDataManager;
use hypertrader_dbs::{DatabaseConnection, init_db};

#[derive(Clone)]
pub struct AppState {
    pub db: DatabaseConnection,
    pub hyperliquid_data_manager: std::sync::Arc<HyperliquidDataManager>,
}

impl AppState {
    pub async fn new() -> Self {
        let db = init_db().await.expect("Failed to connect to database");

        let hyperliquid_data_manager: HyperliquidDataManager =
            HyperliquidDataManager::new(db.clone(), false)
                .await
                .expect("Failed to init hyperliquid data manager");

        hyperliquid_data_manager
            .init()
            .await
            .expect("Failed to init hyperliquid data manager");

        Self {
            db,
            hyperliquid_data_manager: std::sync::Arc::new(hyperliquid_data_manager),
        }
    }
}
