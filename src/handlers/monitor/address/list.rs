use super::*;

#[handler]
pub async fn get_list_subscribed_addresses(
    _req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let app_state = depot
        .obtain::<AppState>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let hyperliquid_data_manager = app_state.hyperliquid_data_manager.clone();
    let addresses = hyperliquid_data_manager.list_subscribed_addresses().await?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::monitor::ListSubscribedAddressesSuccess,
        addresses,
    )));

    Ok(())
}
