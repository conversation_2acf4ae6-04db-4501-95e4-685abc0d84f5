use super::*;
use hyperliquid_rust_sdk::Address;

#[derive(serde::Deserialize, serde::Serialize)]
pub struct UnsubscribeAddressRequest {
    address: String,
}

#[handler]
pub async fn post_unsubscribe_address(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let app_state = depot
        .obtain::<AppState>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let req = req
        .parse_body::<UnsubscribeAddressRequest>()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;

    let address = Address::from_str(&req.address)
        .map_err(|e| anyhow::anyhow!("Failed to parse address: {}", e))?;
    let hyperliquid_data_manager = app_state.hyperliquid_data_manager.clone();

    hyperliquid_data_manager.unsubscribe_user(address).await?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::monitor::UnsubscribeAddressSuccess,
        req.address,
    )));

    Ok(())
}
