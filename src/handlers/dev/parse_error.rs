use super::*;

#[derive(serde::Deserialize, serde::Serialize)]
struct TestStruct {
    name: String,
    age: u32,
}

fn demo_parse_error() -> Result<TestStruct, GlobalError> {
    let test_str = r#"{"name": "test", "age": 10.9}"#;
    serde_json::from_str::<TestStruct>(test_str).map_err(|e| e.into())
}

#[handler]
pub async fn parse_error(
    _req: &mut Request,
    _depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let test_struct = demo_parse_error()?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        test_struct,
    )));

    Ok(())
}
