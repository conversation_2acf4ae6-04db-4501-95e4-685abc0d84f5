use super::*;

#[handler]
pub async fn success(
    _req: &mut Request,
    _depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    #[derive(serde::Deserialize, serde::Serialize)]
    struct TestStruct {
        name: String,
        age: u32,
    }

    let test_str = r#"{"name": "test", "age": 10}"#;
    let test_struct: TestStruct = serde_json::from_str(test_str)?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        test_struct,
    )));

    Ok(())
}
