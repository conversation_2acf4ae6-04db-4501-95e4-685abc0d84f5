use std::collections::{BTreeMap, BTreeSet, HashMap};
use std::hash::Hash;
use std::str::FromStr;
use hyperliquid_rust_sdk::{AllMids, AllMidsData, Leverage};
use salvo::{Depot, FlowCtrl, handler, Request, Response};
use salvo::http::StatusCode;
use salvo::prelude::Json;
use sea_orm::prelude::{BigDecimal, Date, DateTimeUtc};
use stdext::default::default;
use hypertrader_dbs::{ActiveModelTrait, ActiveValue, DatabaseConnection, DbErr, EntityTrait};
// use hypertrader_dbs::utils::{vault, vault_position as vp};
use hypertrader_dbs::schemas;
use hypertrader_dbs::schemas::{user, vault_position, trade_orders, order, signal, signal_statics, deal};
use std::sync::Arc;
use chrono::{Duration, NaiveDateTime, TimeZone, Utc};
use futures_util::TryFutureExt;
use reqwest::redirect::Action;
use sea_orm::{ColumnDef, ConnectionTrait, DeriveColumn, EnumIter, PaginatorTrait, QuerySelect, Schema, Statement};
use sea_orm::sea_query::{Alias, ExprTrait, Index, PostgresQueryBuilder, Query, Table};
use sea_orm::strum::Display;
use serde::{Deserialize, Serialize};
use tokio::sync::Mutex;
use tracing::info;
use tracing_subscriber::fmt::format;
use hypertrader_dbs::ColumnTrait;
use hypertrader_dbs::QueryFilter;
use hypertrader_dbs::schemas::order::Column::OrderId;
use hypertrader_dbs::schemas::order::Model;
// use hypertrader_dbs::utils::user::UserManager;
use hypertrader_utils::apicodes::errors::GlobalError;
use hypertrader_utils::apicodes::success::{GlobalSuccess, SUCCESS_CODES};
// use crate::handlers::dev::create::CreateUserRequest;
use crate::handlers::state::AppState;
// use sea_orm::query::helper::QueryFilter;

pub struct ContractService {
    pub db: DatabaseConnection,
    pub open_positions: HashMap<i64, vault_position::Model>,
    pub orders: HashMap<i32, order::Model>,
    pub users_cache: HashMap<i32, user::Model>,
    // pub all_mids: AllMids,
    pub all_prices: HashMap<String, f64>,
    pub max_leverage: HashMap<String, i32>,
    // pub last_statics_date: Date,
    pub last_statics_time: DateTimeUtc,

}

// 定义市价单结构体
#[derive(serde::Deserialize)]
#[derive(Default)]
pub struct MarketOrder {
    pub user_id: i32,
    pub symbol: String,
    pub order_type: String,
    pub quantity: Option<f64>,

    pub order_action_type: Option<String>,
    pub mode: Option<String>,
    pub leverage: Option<i32>,
    pub tp_price: Option<f64>,
    pub sp_price: Option<f64>,
}

#[derive(serde::Deserialize, Debug)]
#[derive(Default)]
pub struct LimitOrder {
    pub user_id: i32,
    pub symbol: String,
    pub order_type: String,
    pub quantity: Option<f64>,

    pub limit_price: Option<f64>,
    pub order_action_type: Option<String>,
    pub mode: Option<String>,
    pub leverage: Option<i32>,
    pub tp_price: Option<f64>,
    pub sp_price: Option<f64>,

    pub signal_id: Option<i32>,
}

#[derive(Deserialize)]
pub struct SignalClosePosition {
    pub underlying_asset: String,
    pub vault_id: i32,
    pub price: Option<f64>,
}

#[derive(Deserialize)]
pub struct SignalCancel {
    pub underlying_asset: String,
    pub vault_id: i32,
}


#[derive(Deserialize)]
pub struct SignalGenerate {
    pub id: i32,
    pub username: String,
    pub uuid: String,
    pub signal_name: String,
    pub operation_direction: String, // long / short
    pub underlying_asset: String,
    pub vault_id: i32,

    pub mode: String, // cross, isolate

    pub price: f64,
    pub size: f64,

    pub tp_price: Option<f64>,
    pub sp_price: Option<f64>,
    pub leverage: Option<i32>,
}

#[derive(Clone, Default, Debug)]
struct TransactionEntry {
    time: DateTimeUtc,
    symbol: String,
    direction: String,
    px: f64,
    size: f64,
    ntl: f64,
    fee: f64,
    closed_pnl: f64,
}

impl TransactionEntry {
    pub fn from_param(
        time: &str,
        symbol: &str,
        direction: &str,
        px: f64,
        size: f64,
        ntl: f64,
        fee: f64,
        closed_pnl: f64,
    ) -> Self {

        let naive = NaiveDateTime::parse_from_str(time, "%Y/%m/%d %H:%M:%S").unwrap();

        TransactionEntry{
            time: Utc.from_utc_datetime(&naive),
            symbol: symbol.to_string(),
            direction: direction.to_string(),
            px,
            size,
            ntl,
            fee,
            closed_pnl,
        }
    }
}

#[derive(Deserialize)]
pub struct SignalCreate {
    pub user_id: i32,

    // pub id: i32,
    pub username: String,
    pub uuid: String,
    pub signal_name: String,
    pub underlying_asset: String,
}


impl ContractService {
    pub fn get_max_leverage(&self, symbol: &str) -> f64 {
        match self.max_leverage.get(symbol) {
            None => {
                20.0
            }
            Some(l) => { *l as f64 }
        }
    }
    pub async fn load_from_db(&mut self) {

        let manager = sea_orm_migration::manager::SchemaManager::new(&self.db);

        // 2. 检查动态表名是否已存在
        let exists = manager.has_table("symbol_signal").await.unwrap();  // :contentReference[oaicite:0]{index=0}
        if !exists {
            let backend = self.db.get_database_backend();
            let schema = Schema::new(backend);
            let create_stmt = schema.create_table_from_entity(signal::Entity);
            self.db.execute(backend.build(&create_stmt)).await.unwrap();
        }

        let exists = manager.has_table("user").await.unwrap();  // :contentReference[oaicite:0]{index=0}
        if !exists {
            let backend = self.db.get_database_backend();
            let schema = Schema::new(backend);
            let create_stmt = schema.create_table_from_entity(user::Entity);
            self.db.execute(backend.build(&create_stmt)).await.unwrap();
        }

        let users: Vec<user::Model> = user::Entity::find().all(&self.db).await.unwrap();
        for user in users {
            self.users_cache.insert(user.id, user.clone());
        }

        // {
        //     let sig = signal::Entity::find()
        //         .filter(signal::Column::VaultId.eq(2))
        //         .filter(signal::Column::UnderlyingAsset.eq("BTC"))
        //         .one(&self.db).await.unwrap();
        //
        //     if let Some(sig) = sig.as_ref() {
        //         println!("{:?}", sig);
        //     }
        //     assert!(sig.is_some());
        //     std::process::exit(0);
        // }

        let exists = manager.has_table("trade_orders").await.unwrap();  // :contentReference[oaicite:0]{index=0}
        if !exists {
            let backend = self.db.get_database_backend();
            let schema = Schema::new(backend);
            let create_stmt = schema.create_table_from_entity(order::Entity);
            self.db.execute(backend.build(&create_stmt)).await.unwrap();
        }

        let orders: Vec<order::Model> = order::Entity::find().all(&self.db).await.unwrap();
        for order in orders {
            if order.is_active {
                println!("from db order, order_id: {}, user_id: {}, symbol: {}, order_type: {}, is_active: {}, price: {}, size: {}",
                         order.order_id, order.user_id, order.symbol, order.order_type, order.is_active, order.limit_price, order.size
                );
                self.orders.insert(order.order_id, order);
            }
        }


        let exists = manager.has_table("vault_position").await.unwrap();  // :contentReference[oaicite:0]{index=0}
        if !exists {
            let backend = self.db.get_database_backend();
            let schema = Schema::new(backend);
            let create_stmt = schema.create_table_from_entity(vault_position::Entity);
            self.db.execute(backend.build(&create_stmt)).await.unwrap();
        }

        let positions: Vec<vault_position::Model> = vault_position::Entity::find().all(&self.db).await.unwrap();
        for position in positions {
            if position.is_open {
                println!("from db position, position_id: {}, user_id: {}, symbol: {}, size: {}, is_open: {}, entry_price: {}",
                         position.position_id, position.user_id, position.symbol, position.size, position.is_open, position.entry_price
                );

                self.open_positions.insert(position.position_id, position);
            }
        }

        let exists = manager.has_table("trade_deals").await.unwrap();  // :contentReference[oaicite:0]{index=0}
        if !exists {
            let backend = self.db.get_database_backend();
            let schema = Schema::new(backend);
            let create_stmt = schema.create_table_from_entity(deal::Entity);
            self.db.execute(backend.build(&create_stmt)).await.unwrap();
        }

        // let mut tab =  Table::create();
        // // 构造建表语句，设置动态表名
        // let mut create_stmt = tab
        //     .table(Alias::new("symbol_signal"))
        //     .if_not_exists();
        // create_stmt.col(&mut schema.get_column_def::<signal::Entity>(signal::Column::SigId));
        // // 指定主键约束
        // create_stmt.primary_key(Index::create().col(signal::Column::SigId).primary());
        // // 生成 SQL 并执行
        // self.db.execute(backend.build(create_stmt)).await.unwrap();

        return;

        let data_map = [
            TransactionEntry::from_param(
                "2025/3/17 07:30:30", "BTC","Open Long",82311.0 as f64,0.06066,4992.9852599999995,1.677642,-1.677642,
            ),
            TransactionEntry::from_param(
		"2025/3/17 10:00:54","BTC","Close Long",83182 as f64,0.06066,5045.82012,1.695394,51.139466,
            ),
            TransactionEntry::from_param(
		"2025/3/18 21:36:18","BTC","Open Long",81800 as f64,0.0641,5243.38,0.503364,-0.503364,
            ),

            TransactionEntry::from_param(
		"2025/3/19 20:04:29","BTC","Close Long",83723 as f64,0.0641,5366.6443,1.803192,121.46110800000001,
            ),

            TransactionEntry::from_param(
		"2025/3/19 20:56:51","BTC","Open Long",83614 as f64,0.0699,5844.618600000001,1.963791,-1.963791,
            ),

            TransactionEntry::from_param(
		"2025/3/19 21:34:52","BTC","Close Long",83734.882546495 as f64,0.0699,5853.068290000001,1.966625,6.483065,
            ),

            TransactionEntry::from_param(
		"2025/3/19 21:45:04","BTC","Open Long",83947 as f64,0.06993,5870.413710000001,1.972458,-1.972458,
            ),

            TransactionEntry::from_param(
		"2025/3/19 21:46:26","BTC","Close Long",84066 as f64,0.06993,5878.73538,1.975254,6.346416,
            ),

            TransactionEntry::from_param(
		"2025/3/19 21:56:48","BTC","Open Short",84300 as f64,0.03025,2550.075,0.244806,-0.244806,
            ),

            TransactionEntry::from_param(
		"2025/3/19 21:56:49","BTC","Open Short",84300 as f64,0.03559,3000.2369999999996,0.288022,-0.288022,
            ),

            TransactionEntry::from_param(
		"2025/3/19 21:56:50","BTC","Open Short",84300 as f64,0.00395,332.985,0.031966,-0.031966,
            ),

            TransactionEntry::from_param(
		"2025/3/19 22:00:06","BTC","Close Short",84194.9577303339 as f64,0.06979,5875.966100000003,1.974322,5.356578,
            ),

            TransactionEntry::from_param(
		"2025/3/20 20:01:59","BTC","Open Long",85420 as f64,0.06917,5908.501399999999,1.985256,-1.985256,
            ),

            TransactionEntry::from_param(
		"2025/3/20 23:01:14","BTC","Close Long",85794.6862801793 as f64,0.06917,5934.418450000002,1.993957,23.923093,
            ),

            TransactionEntry::from_param(
		"2025/3/21 09:17:45","BTC","Open Long",84484.3257056957 as f64,0.05987,5058.076580000001,1.699507,-1.699507,
            ),

            TransactionEntry::from_param(
		"2025/3/21 10:02:37","BTC","Close Long",84802.0150325706 as f64,0.05987,5077.0966400000025,1.7059,17.315699,
            ),

            TransactionEntry::from_param(
		"2025/3/26 21:26:03","BTC","Open Long",87445 as f64,0.05801,5072.68445,1.704421,-1.704421,
            ),

            TransactionEntry::from_param(
		"2025/3/26 22:01:04","BTC","Open Long",86935 as f64,0.0335,2912.3225,0.978539,-0.978539,
            ),

            TransactionEntry::from_param(
		"2025/3/27 03:07:37","BTC","Close Long",85975 as f64,0.09151,7867.572249999999,2.643504,-120.06913599999999,
            ),

            TransactionEntry::from_param(
		"2025/4/7 21:55:20","BTC","Open Long",77894.5791316526 as f64,0.05712,4449.338359999996,1.494976,-1.494976,
            ),

            TransactionEntry::from_param(
		"2025/4/7 21:57:12","BTC","Close Long",78219 as f64,0.01428,1116.96732,0.3753,4.25856,
            ),

            TransactionEntry::from_param(
		"2025/4/7 21:57:46","BTC","Close Long",78209.9005602241 as f64,0.04284,3350.5121400000003,1.125769,12.385991,
            ),

            TransactionEntry::from_param(
		"2025/4/8 22:18:30","BTC","Open Long",80045 as f64,0.0433,3465.9485,1.164557,-1.164557,
            ),

            TransactionEntry::from_param(
		"2025/4/8 22:55:03","BTC","Close Long",78775 as f64,0.0433,3410.9575,1.146081,-56.137081,
            ),

            TransactionEntry::from_param(
		"2025/4/10 01:23:55","BTC","Open Long",80800 as f64,0.05077,4102.216,1.378344,-1.378344,
            ),

            TransactionEntry::from_param(
		"2025/4/10 05:09:02","BTC","Close Long",83309 as f64,0.05077,4229.59793,1.421143,125.960787,
            ),

            TransactionEntry::from_param(
		"2025/4/10 05:38:58","BTC","Open Short",83063 as f64,0.02248,1867.25624,0.627397,-0.627397,
            ),

            TransactionEntry::from_param(
		"2025/4/10 05:40:34","BTC","Close Short",82938.4635231317 as f64,0.02248,1864.4566600000005,0.626456,2.173124,
            ),

            TransactionEntry::from_param(
		"2025/4/10 22:20:09","BTC","Open Long",81128 as f64,0.05763,4675.40664,1.570936,-1.570936,
            ),

            TransactionEntry::from_param(
		"2025/4/10 23:16:06","BTC","Close Long",80563.510150963 as f64,0.05763,4642.875089999998,1.559999,-34.091549,
            ),

            TransactionEntry::from_param(
		"2025/4/10 23:58:13","BTC","Open Long",78790 as f64,0.05763,4540.6677,1.525663,-1.525663,
            ),

            TransactionEntry::from_param(
		"2025/4/11 00:01:54","BTC","Close Long",78523 as f64,0.05763,4525.28049,1.520494,-16.907704,
            ),

            TransactionEntry::from_param(
		"2025/4/11 00:16:08","BTC","Open Long",78772 as f64,0.05587,4400.99164,1.478733,-1.478733,
            ),

            TransactionEntry::from_param(
		"2025/4/11 00:45:20","BTC","Close Long",79184.3472346519 as f64,0.05587,4424.029480000002,1.486472,21.551368,
            ),

            TransactionEntry::from_param(
		"2025/4/11 18:28:37","BTC","Open Long",82770.0599078341 as f64,0.00217,179.61103,0.060347,-0.060347,
            ),

        ];

        let mut map: BTreeMap<DateTimeUtc, TransactionEntry> = BTreeMap::new();
        for data in data_map {
            map.insert(data.time, data.clone());
        }

        // 2. 计算 “30 天前” 的时间下界
        let lower_bound = Utc::now() - Duration::days(30*12);

        let mut win_count = 0;
        let mut total_count = 0;
        // 3. 执行范围查询：取出所有键 ≥ lower_bound
        println!("Entries in the last N days:");
        for (dt, val) in map.range(lower_bound..) {
            // println!("  {} -> {:?}", dt, val);
            if val.closed_pnl > 0.0 {
                win_count += 1;
            }
            total_count += 1;
        }

        let win_rate = if total_count==0 {0.0} else { win_count as f64 / total_count as f64 };
        println!("Wins: {}, total: {}, win_rate: {}", win_count, total_count, win_rate);

        let new_signal = signal::ActiveModel {
            // id: ActiveValue::Set(1),
            username: ActiveValue::Set("test0".to_string()),
            uuid: ActiveValue::Set("uuid000".to_string()),
            signal_type: ActiveValue::Set("candidate".to_string()),
            signal_name: ActiveValue::Set("signal unnamed".to_string()),
            signal_pnl: ActiveValue::Set(0.0),

            // initial_capital: ActiveValue::Set(100.0),
            // latest_assets: ActiveValue::Set(109.0),
            running_status: ActiveValue::Set("running".to_string()),
            consecutive_wins: ActiveValue::Set(0),
            subscribers: ActiveValue::Set(0),
            operation_direction: ActiveValue::Set("long".to_string()),
            underlying_asset: ActiveValue::Set("BTC".to_string()),
            monthly_return_rate: ActiveValue::Set(0.0),
            monthly_alpha: ActiveValue::Set(0.0),
            annual_win_rate: ActiveValue::Set(win_rate),
            cumulative_income: ActiveValue::Set(0.0),

            sharpe_ratio: ActiveValue::Set(0.0),
            three_yield: ActiveValue::Set(0.0),
            seven_yield: ActiveValue::Set(0.0),
            max_drawdown_7days: ActiveValue::Set(0.0),
            running_time: ActiveValue::Set(0.0),
            historical_win_rate: ActiveValue::Set(0.0),
            profit_loss_count: ActiveValue::Set(0.0),
            profit_loss_ratio: ActiveValue::Set(0.0),
            confidence: ActiveValue::Set("low".to_string()),  // high, middle, low,
            evaluation_status: ActiveValue::Set("unqualified".to_string()), // qualified, unqualified, qualifying
            review: ActiveValue::Set("".to_string()),

            user_id: ActiveValue::Set(1),
            ..Default::default()
        };


        let sig = new_signal.insert(&self.db).await.unwrap();


    }
    // 开仓逻辑
    pub async fn open_position(
        &mut self,
        user_id: i32,
        symbol: &str,
        mode: &str,
        mark_price: f64,
        quantity: f64,
        tp_price: Option<f64>,
        sp_price: Option<f64>,
        order_action_type: &str,
        leverge: Option<i32>,
    ) -> Result<vault_position::Model, DbErr> {
        // let user:Option<user::Model> = user::Entity::find_by_id(user_id).one(&self.db).await?;
        // if let Some(user) = user
        if true
        {
            if self.open_positions.iter().filter(|(_, p)| p.user_id == user_id && p.symbol == symbol).count() > 0 {
                let mut pos_vec = self.open_positions.iter_mut().filter(|(_, p)| p.user_id == user_id && p.symbol == symbol).collect::<Vec<_>>();
                assert_eq!(pos_vec.len(), 1);
                // let required_funds = entry_price * quantity / leverge.unwrap_or(1) as f64;
                // let user_fund = user.fund.clone();
                // println!("user_fund:{:?}",user_fund);
                // if user.fund >= required_funds
                if true
                {

                    // // 扣除用户资金
                    // let mut user_model: user::ActiveModel = user.into();
                    // user_model.fund = ActiveValue::Set(user_fund - required_funds);
                    // // user_model.fund = user_fund - required_funds;
                    // user_model.update(&self.db).await?;

                    let position = &mut *(pos_vec[0].1);

                    let entry_price = (mark_price * quantity + position.entry_price * position.size) / (quantity + position.size);
                    position.size += quantity;
                    position.entry_price = entry_price;

                    // 更新仓位
                    let mut new_position: vault_position::ActiveModel = (*position).clone().into();

                    let position: vault_position::Model = new_position.update(&self.db).await.unwrap();
                    println!("position_info_to_print:{:?}", position.clone());

                    let new_deal = deal::ActiveModel {
                        user_id: ActiveValue::Set(user_id),
                        time: ActiveValue::Set(Utc::now()),
                        coin: ActiveValue::Set(symbol.to_string()),
                        dir:  ActiveValue::Set(format!("open_{}", position.direction)),
                        px:   ActiveValue::Set(mark_price),
                        sz:   ActiveValue::Set(quantity),
                        fee:  ActiveValue::Set(0.0),
                        closed_pnl: ActiveValue::Set(0.0),
                        closed_pnl_rate: ActiveValue::Set(0.0),
                        ..Default::default()
                    };
                    let deal: deal::Model = new_deal.insert(&self.db).await?;

                    // self.open_positions.insert(position.position_id, position.clone());
                    // for(pos_id, postion) in self.open_positions.iter(){
                    //     println!("position_info:{:?}---{:?}",postion.position_id,pos_id);
                    // }
                    Ok(position)
                } else {
                    Err(DbErr::Custom("Insufficient funds to add position".to_string()))
                }
            } else {
                // let required_funds = entry_price * quantity / leverge.unwrap_or(1) as f64;
                // let user_fund = user.fund.clone();
                // println!("user_fund:{:?}",user_fund);
                // if user.fund >= required_funds
                if true
                {

                    // // 扣除用户资金
                    // let mut user_model: user::ActiveModel = user.into();
                    // user_model.fund = ActiveValue::Set(user_fund - required_funds);
                    // // user_model.fund = user_fund - required_funds;
                    // user_model.update(&self.db).await?;

                    // 创建新仓位
                    let new_position = vault_position::ActiveModel {
                        user_id: ActiveValue::Set(user_id),
                        symbol: ActiveValue::Set(symbol.to_string()),
                        mode: ActiveValue::Set(mode.to_string()),
                        entry_price: ActiveValue::Set(mark_price),
                        // current_price: ActiveValue::Set(entry_price),
                        size: ActiveValue::Set(quantity),
                        is_open: ActiveValue::Set(true),
                        tp_price: ActiveValue::Set(tp_price),
                        sp_price: ActiveValue::Set(sp_price),
                        leverage: ActiveValue::Set(leverge),
                        direction: ActiveValue::Set(order_action_type.to_string()),
                        ..Default::default()
                    };
                    let position: vault_position::Model = new_position.insert(&self.db).await?;
                    println!("position_info_to_print:{:?}", position.clone());
                    self.open_positions.insert(position.position_id, position.clone());
                    for (pos_id, postion) in self.open_positions.iter() {
                        println!("position_info:{:?}---{:?}", postion.position_id, pos_id);
                    }


                    let new_deal = deal::ActiveModel {
                        user_id: ActiveValue::Set(user_id),
                        time: ActiveValue::Set(Utc::now()),
                        coin: ActiveValue::Set(symbol.to_string()),
                        dir:  ActiveValue::Set(format!("open_{}", position.direction)),
                        px:   ActiveValue::Set(mark_price),
                        sz:   ActiveValue::Set(quantity),
                        fee:  ActiveValue::Set(0.0),
                        closed_pnl: ActiveValue::Set(0.0),
                        closed_pnl_rate: ActiveValue::Set(0.0),
                        ..Default::default()
                    };
                    let deal: deal::Model = new_deal.insert(&self.db).await?;

                    if let Some(sig) =  signal::Entity::find()
                        .filter(signal::Column::UserId.eq(position.user_id))
                        .filter(signal::Column::UnderlyingAsset.eq(symbol))
                        .one(&self.db).await? {
                        let mut new_sig :signal::ActiveModel = sig.into();
                        if new_sig.running_status.clone().unwrap() != "running" {
                            new_sig.running_status = ActiveValue::Set(String::from("running"));
                        }
                        new_sig.operation_direction = ActiveValue::Set(deal.dir);

                        let _ = new_sig.update(&self.db).await?;
                    }

                    Ok(position)
                } else {
                    Err(DbErr::Custom("Insufficient funds to open position".to_string()))
                }
            }
        } else {
            Err(DbErr::RecordNotFound("User not found".to_string()))
        }
    }

    pub fn calc_profit(position: &vault_position::Model, price: f64) -> f64 {
        let mut profit = (price - position.entry_price) * position.size;
        if position.direction == "short" {
            profit = -profit;
        }
        profit
    }

    pub fn calc_profit_with_size(position: &vault_position::Model, price: f64, size: f64) -> f64 {
        let mut profit = (price - position.entry_price) * size;
        if position.direction == "short" {
            profit = -profit;
        }
        profit
    }

    // 平仓逻辑
    pub async fn update_fund(db: &DatabaseConnection, users_cache: &mut HashMap<i32, user::Model>, user_id: i32, fund: f64) -> Result<(), DbErr> {
        let user = users_cache.get_mut(&user_id).unwrap();
        user.fund += fund;
        let user_model: user::ActiveModel = user.clone().into();
        user_model.update(db).await?;
        Ok(())
    }
    pub async fn close_position(
        &mut self,
        position_id: i64,
        mark_price: f64,
        size: f64,
        close_reason: &str,
    ) -> Result<(), DbErr> {
        if size >= self.open_positions[&position_id].size {
            // close all
            if let Some(mut position) = self.open_positions.remove(&position_id) {
                if position.is_open {

                    // let refun = position.entry_price * position.size / position.leverage.unwrap_or(1) as f64 + Self::calc_profit(&position, close_price);
                    // 查找用户
                    // let user = user::Entity::find_by_id(position.user_id)
                    //     .one(&self.db)
                    //     .await?;
                    if self.users_cache.contains_key(&position.user_id) {
                        // let user_fund = user.fund.clone();
                        //
                        // // 更新用户资金
                        // let mut user_model:user::ActiveModel = user.into();
                        //
                        // user_model.fund = ActiveValue::Set(user_fund + refun);
                        // let updated_user = user_model.update(&self.db).await?;
                        // self.update_fund(position.user_id, self.users_cache.get(&position.user_id).unwrap().fund + refun).await?;
                        let profit = Self::calc_profit_with_size(&position, mark_price, size);
                        let new_fund = self.users_cache.get(&position.user_id).unwrap().fund + profit;
                        Self::update_fund(&self.db, &mut self.users_cache, position.user_id, new_fund).await?;

                        // 关闭仓位
                        position.is_open = false;
                        let mut position_model: vault_position::ActiveModel = position.clone().into();
                        position_model.is_open = ActiveValue::Set(false);
                        let updated_position = position_model.update(&self.db).await?;

                        let new_deal = deal::ActiveModel {
                            user_id: ActiveValue::Set(position.user_id),
                            time: ActiveValue::Set(Utc::now()),
                            coin: ActiveValue::Set(position.symbol.clone()),
                            dir:  ActiveValue::Set(format!("close_{}", position.direction)),
                            px:   ActiveValue::Set(mark_price),
                            sz:   ActiveValue::Set(size),
                            fee:  ActiveValue::Set(0.0),
                            closed_pnl: ActiveValue::Set(profit),
                            closed_pnl_rate: ActiveValue::Set(profit/self.margin_required(&position)),
                            ..Default::default()
                        };
                        let deal: deal::Model = new_deal.insert(&self.db).await?;

                        if let Some(sig) =  signal::Entity::find()
                            .filter(signal::Column::UserId.eq(position.user_id))
                            .filter(signal::Column::UnderlyingAsset.eq(position.symbol))
                            .one(&self.db).await? {
                            let mut new_sig :signal::ActiveModel = sig.into();
                            if new_sig.running_status.clone().unwrap() != close_reason {
                                new_sig.running_status = ActiveValue::Set(String::from(close_reason));
                            }
                            new_sig.signal_pnl = ActiveValue::Set(0.0); 
                            new_sig.operation_direction = ActiveValue::Set("close".to_string());

                            let _ = new_sig.update(&self.db).await?;
                        }

                        Ok(())
                    } else {
                        Err(DbErr::RecordNotFound("User not found".to_string()))
                    }
                } else {
                    Err(DbErr::Custom("Position is already closed".to_string()))
                }
            } else {
                Err(DbErr::RecordNotFound("Position not found".to_string()))
            }
        } 
        else {
            if let Some(mut position) = self.open_positions.get_mut(&position_id) {
                if position.is_open {
                    let profit = Self::calc_profit(&position, mark_price);
                    // 查找用户
                    // let user = user::Entity::find_by_id(position.user_id)
                    //     .one(&self.db)
                    //     .await?;
                    // if let Some(user) = user {
                    if self.users_cache.contains_key(&position.user_id) {
                        // let user_fund = user.fund.clone();
                        //
                        // // 更新用户资金
                        // let mut user_model:user::ActiveModel = user.into();
                        //
                        // user_model.fund = ActiveValue::Set(user_fund + refun);
                        // let updated_user = user_model.update(&self.db).await?;

                        // self.update_fund(position.user_id, self.users_cache.get(&position.user_id).unwrap().fund + refun).await?;
                        let new_fund = self.users_cache.get(&position.user_id).unwrap().fund + profit;
                        Self::update_fund(&self.db, &mut self.users_cache, position.user_id, new_fund).await?;

                        // 更新仓位
                        // position.is_open = false;
                        position.size -= size;

                        let mut position_model: vault_position::ActiveModel = position.clone().into();
                        // position_model.is_open = ActiveValue::Set(false);
                        let updated_position = position_model.update(&self.db).await?;

                        let new_deal = deal::ActiveModel {
                            user_id: ActiveValue::Set(position.user_id),
                            time: ActiveValue::Set(Utc::now()),
                            coin: ActiveValue::Set(position.symbol.clone()),
                            dir:  ActiveValue::Set(format!("close_{}", position.direction)),
                            px:   ActiveValue::Set(mark_price),
                            sz:   ActiveValue::Set(size),
                            fee:  ActiveValue::Set(0.0),
                            closed_pnl: ActiveValue::Set(profit),
                            closed_pnl_rate: ActiveValue::Set(profit/Self::margin_required2(&position, mark_price)),
                            ..Default::default()
                        };
                        let deal: deal::Model = new_deal.insert(&self.db).await?;

                        Ok(())
                    } else {
                        Err(DbErr::RecordNotFound("User not found".to_string()))
                    }
                } else {
                    Err(DbErr::Custom("Position is already closed".to_string()))
                }
            } else {
                Err(DbErr::RecordNotFound("Position not found".to_string()))
            }
        }
    }


    pub async fn check_is_order_conflict(&self, user_id: i32, symbol: &str, order_action_type: &str, leverage: Option<i32>) -> Result<(), DbErr> {
        let conflict_set = self.open_positions
            .iter().filter(|(_, p)| p.is_open && p.user_id == user_id && p.symbol == symbol && (p.direction != order_action_type || p.leverage != leverage)).collect::<Vec<_>>();
        if !conflict_set.is_empty() {
            return Err(DbErr::Custom("Order action type or leverge conflict with current vault positions list".to_string()));
        }

        let conflict_set = self.orders
            .iter().filter(|(_, p)| p.is_active && p.user_id == user_id && p.symbol == symbol && (p.order_action_type != order_action_type || p.leverage != leverage)).collect::<Vec<_>>();
        if !conflict_set.is_empty() {
            return Err(DbErr::Custom("Order action type or leverge conflict with current order list".to_string()));
        }

        Ok(())
    }

    pub fn has_cross_position(&self) -> bool {
        self.open_positions.iter().any(|(_, p)| p.mode == "cross")
    }
    // 创建订单
    // 创建生成限价单的方法
    pub async fn create_limit_order(
        &mut self,
        order: LimitOrder
    ) -> Result<order::Model, DbErr> {
        if order.order_type != "open" && order.order_type != "close" {
            return Err(DbErr::Custom("Order type not supported".to_string()));
        }
        if order.order_type == "open" {
            if order.order_action_type.is_none() || order.limit_price.is_none() || order.mode.is_none() {
                return Err(DbErr::Custom("missing order_action_type/limit_price/mode".to_string()));
            }
            if order.mode.as_ref().unwrap() == "isolate" && self.has_cross_position() {
                return Err(DbErr::Custom("please remove cross all position".to_string()));
            }
            if order.mode.as_ref().unwrap() != "cross" && order.mode.as_ref().unwrap() != "isolate" {
                return Err(DbErr::Custom("Order mode must be cross or isolate".to_string()));
            }

            if order.order_action_type.as_ref().unwrap() != "long" && order.order_action_type.as_ref().unwrap() != "short" {
                return Err(DbErr::Custom("Order action must be long or short".to_string()));
            }

            if order.quantity.is_none() {
                return Err(DbErr::Custom("must specify quantity with open position".to_string()));
            }

            self.check_is_order_conflict(order.user_id, &order.symbol, &order.order_action_type.as_ref().unwrap(), order.leverage).await?;
        }
        if order.quantity.is_some() && order.quantity.unwrap() <= 0.0 {
            return Err(DbErr::Custom("Order quantity must be positive".to_string()));
        }
        if order.leverage.is_some() && order.leverage.unwrap() <= 0 {
            return Err(DbErr::Custom("Order leverage must be positive".to_string()));
        }

        // let user :Option<user::Model> = user::Entity::find_by_id(order.user_id).one(&self.db).await?;
        // if let Some(user) = user {
        if order.order_type == "open" {
            if self.users_cache.contains_key(&order.user_id)
            {
                let required_funds = order.limit_price.unwrap() * order.quantity.unwrap() / order.leverage.unwrap_or(1) as f64;
                // let user_fund = user.fund.clone();
                // println!("user_fund:{:?}",user_fund);
                let user_fund = self.avail_value(order.user_id)?;
                if user_fund >= required_funds {
                    // Self::update_fund(&self.db, &mut self.users_cache, order.user_id, user_fund - required_funds).await?;

                    // 扣除用户资金
                    // let mut user_model: user::ActiveModel = user.into();
                    // user_model.fund = ActiveValue::Set(user_fund - required_funds);
                    // // user_model.fund = user_fund - required_funds;
                    // user_model.update(&self.db).await?;

                } else {
                    return Err(DbErr::Custom("Insufficient funds to create limit order".to_string()));
                }
            } else {
                return Err(DbErr::RecordNotFound("User not found".to_string()))
            }
        }

        let new_order = order::ActiveModel {
            user_id: ActiveValue::Set(order.user_id),
            symbol: ActiveValue::Set(order.symbol.to_string()),
            order_type: ActiveValue::Set(order.order_type.to_string()),
            mode: ActiveValue::Set(order.mode.unwrap_or("cross".to_string()).to_string()),
            // order_action_type: ActiveValue::Set("long".to_string()),
            order_action_type: ActiveValue::Set(order.order_action_type.unwrap_or("close".to_string()).to_string()),
            // order_action_type: ActiveValue::Set(order_type),
            limit_price: ActiveValue::Set(order.limit_price.unwrap_or(-1.0)),
            size: ActiveValue::Set(order.quantity.unwrap_or(-1.0)),
            is_active: ActiveValue::Set(true),
            leverage: ActiveValue::Set(order.leverage),
            tp_price: ActiveValue::Set(order.tp_price),
            sp_price: ActiveValue::Set(order.sp_price),
            signal_id: ActiveValue::Set(order.signal_id),

            ..Default::default()
        };
        let order = new_order.insert(&self.db).await?;
        self.orders.insert(order.order_id, order.clone());

        // if let Some(sig) =  signal::Entity::find()
        //     .filter(signal::Column::UserId.eq(order.user_id))
        //     .filter(signal::Column::UnderlyingAsset.eq(order.symbol.clone()))
        //     .one(&self.db).await? {
        //     let mut new_sig :signal::ActiveModel = sig.into();
        //     if new_sig.running_status.clone().unwrap() != "running" {
        //         new_sig.running_status = ActiveValue::Set(String::from("running"));
        //     }
        //
        //     let dir = format!("{}_{}", order.order_type, order.order_action_type);
        //     new_sig.operation_direction = ActiveValue::Set(dir);
        //     let _ = new_sig.update(&self.db).await?;
        // }

        println!("order_info_to_print_in_limit_order:{:?}", order.clone());
        Ok(order)
    }
    pub async fn cancel_order(
        &mut self,
        order_id: i32
    ) -> Result<(), DbErr> {
        match self.orders.remove(&order_id) {
            None => {
                Err(DbErr::RecordNotFound("Order not found".to_string()))
            }
            Some(order) => {
                let mut order_model: order::ActiveModel = order.into();
                order_model.is_active = ActiveValue::Set(false);
                order_model.update(&self.db).await?;

                Ok(())
            }
        }
    }

    // 处理限价单
    // .filter(order::Column::OrderType.eq("open"))
    // .filter(order::Column::IsFilled.eq(false))

    pub async fn process_limit_orders(&mut self, symbol: &str, current_price: f64) -> Result<(), DbErr> {
        // let open_orders = order::Entity::find()
        //     .filter(order::Column::IsActive.eq(false))
        //     .all(&self.db)
        //     .await?;
        let open_orders = self
            .orders
            .values()
            // .filter(|p| p.user_id == 4 && p.is_active)
            .filter(|p| p.is_active && p.symbol == symbol)
            .cloned()
            .collect::<Vec<_>>();

        // if symbol == "ETH" {
        //     println!("debug");
        // }
        for order in open_orders {
            if (order.order_type == "open" && order.limit_price >= current_price)
                || (order.order_type == "close" && order.limit_price <= current_price)
            {
                if order.order_type == "open" {
                    assert!(order.size > 0.0);
                    match self
                        .open_position(
                            order.user_id,
                            symbol,
                            &order.mode,
                            current_price,
                            order.size,
                            order.tp_price,
                            order.sp_price,
                            &order.order_action_type,
                            order.leverage,
                        )
                        .await
                    {
                        Ok(_) => {

                            // // let user :Option<user::Model> = user::Entity::find_by_id(order.user_id).one(&self.db).await?;
                            // // if let Some(user) = user {
                            // if self.users_cache.contains_key(&order.user_id) {
                            //
                            //     let lock_funds = order.price * order.size / order.leverage.unwrap_or(1) as f64;
                            //     let required_funds = current_price * order.size / order.leverage.unwrap_or(1) as f64;
                            //
                            //     assert!(lock_funds >= required_funds);
                            //
                            //     let user_fund = self.users_cache[&order.user_id].fund;
                            //     Self::update_fund(&self.db, &mut self.users_cache, order.user_id, user_fund + lock_funds-required_funds).await?;
                            //
                            //         // let user_fund = user.fund.clone();
                            //     // // println!("user_fund:{:?}",user_fund);
                            //     // // 返还用户资金
                            //     // let mut user_model: user::ActiveModel = user.into();
                            //     // user_model.fund = ActiveValue::Set(user_fund + lock_funds-required_funds);
                            //     // // user_model.fund = user_fund - required_funds;
                            //     // user_model.update(&self.db).await?;
                            //
                            //
                            // } else {
                            //     return Err(DbErr::RecordNotFound("User not found in refun limit order".to_string()))
                            // }

                            // if order.signal_id.is_some() {
                            //     if let Some(mut sig) = signal::Entity::find_by_id(order.signal_id.unwrap()).one(&self.db).await? {
                            //         // sig.state = "running".to_string();
                            //         let mut new_sig: signal::ActiveModel = sig.into();
                            //         new_sig.state = ActiveValue::Set("running".to_string());
                            //         new_sig.update(&self.db).await?;
                            //     }
                            // }

                            let order_id = order.clone().order_id;
                            let mut order_model: order::ActiveModel = order.into();
                            order_model.is_active = ActiveValue::Set(false);
                            order_model.update(&self.db).await?;

                            // 将内存中的order信息移除
                            self.orders.remove(&order_id);
                        }
                        Err(err) => {
                            eprintln!("Error opening position for order {}: {}", order.order_id, err);
                        }
                    }
                } else if order.order_type == "close" {
                    let positions = self
                        .open_positions
                        .values()
                        .filter(|p| p.user_id == order.user_id && p.is_open && p.symbol == symbol)
                        .cloned()
                        .collect::<Vec<_>>();

                    if !positions.is_empty() {
                        assert_eq!(positions.len(), 1);
                        // for position in positions {
                        let position = positions[0].clone();
                        match self.close_position(position.position_id, current_price, if order.size > 0.0 { order.size } else { position.size }, "closed").await {
                            Ok(_) => {
                                let mut order_model: order::ActiveModel = order.clone().into();
                                order_model.is_active = ActiveValue::Set(false);
                                order_model.update(&self.db).await?;

                                // if let Some(mut sig) = signal::Entity::find()
                                //     .filter(signal::Column::VaultId.eq(order.user_id))
                                //     .filter(signal::Column::UnderlyingAsset.eq(symbol))
                                //     .one(&self.db).await? {
                                //     // sig.state = "closed".to_string();
                                //     let mut new_sig: signal::ActiveModel = sig.into();
                                //     new_sig.state = ActiveValue::Set("closed".to_string());
                                //     new_sig.update(&self.db).await?;
                                // }
                            }
                            Err(err) => {
                                eprintln!(
                                    "Error closing position {} for order {}: {}",
                                    position.position_id, order.order_id, err
                                );
                            }
                            // }
                        }

                        self.orders.remove(&order.order_id);
                    }
                }
            }
        }
        Ok(())
    }

    // 处理止盈止损
    pub async fn process_stop_orders(&mut self, symbol: &str, current_price: f64) -> Result<(), DbErr> {
        let positions_to_close = self
            .open_positions
            .iter()
            .map(|(_, position)| {
                if position.symbol != symbol {
                    return (false, position.position_id, "");
                }
                if let Some(tp_price) = position.tp_price {
                    if current_price >= tp_price {
                        return (true, position.position_id, "reach_tp");
                    }
                }
                if let Some(sp_price) = position.sp_price {
                    if current_price <= sp_price {
                        return (true, position.position_id, "reach_sp");
                    }
                }
                return (false, position.position_id, "");
            })
            .filter(|(ok, p, t)| {
                *ok
            })
            .collect::<Vec<_>>();

        for (ok, position_id, t) in positions_to_close {
            assert!(ok);
            println!("stop position, position_id: {}, symbol: {}, current_price: {}", position_id, symbol, current_price);
            let user_id = self.open_positions[&position_id].user_id;

            match self.close_position(position_id, current_price, self.open_positions[&position_id].size, t).await {
                Ok(_) => {
                    println!(
                        "Position {} closed due to take profit/stop loss at price {}",
                        position_id, current_price
                    );

                    // if let Some(mut sig) = signal::Entity::find()
                    //     .filter(signal::Column::VaultId.eq(user_id))
                    //     .filter(signal::Column::UnderlyingAsset.eq(symbol))
                    //     .one(&self.db).await? {
                    //     let mut new_sig: signal::ActiveModel = sig.into();
                    //     new_sig.state = ActiveValue::Set(t.to_string());
                    //     new_sig.update(&self.db).await?;
                    // }
                }
                Err(err) => {
                    eprintln!(
                        "Error closing position {} due to take profit/stop loss: {}",
                        position_id, err
                    );
                }
            }
        }
        Ok(())
    }

    // 处理市价单
    pub async fn create_market_order(
        &mut self,
        order: MarketOrder,
        // current_price: f64,
    ) -> Result<(), DbErr> {
        if order.order_type != "open" && order.order_type != "close" {
            return Err(DbErr::Custom("Order type not supported".to_string()));
        }

        if order.order_type == "open" {
            if order.order_action_type.is_none() || order.mode.is_none() {
                return Err(DbErr::Custom("missing order_action_type/mode".to_string()));
            }
            if order.mode.as_ref().unwrap() == "isolate" && self.has_cross_position() {
                return Err(DbErr::Custom("please remove cross all position".to_string()));
            }
            if order.mode.as_ref().unwrap() != "cross" && order.mode.as_ref().unwrap() != "isolate" {
                return Err(DbErr::Custom("Order mode must be cross or isolate".to_string()));
            }

            if order.order_action_type.as_ref().unwrap() != "long" && order.order_action_type.as_ref().unwrap() != "short" {
                return Err(DbErr::Custom("Order action must be long or short".to_string()));
            }

            self.check_is_order_conflict(order.user_id, &order.symbol, &order.order_action_type.as_ref().unwrap(), order.leverage).await?;
        }

        if let Some(&current_price) = self.all_prices.get(&order.symbol) {
            if order.order_type == "open" {

                // let user :Option<user::Model> = user::Entity::find_by_id(order.user_id).one(&self.db).await?;
                // if let Some(user) = user {
                if self.users_cache.contains_key(&order.user_id) {
                    let required_funds = current_price * order.quantity.unwrap() / order.leverage.unwrap_or(1) as f64;
                    // let user_fund = user.fund.clone();
                    // // println!("user_fund:{:?}",user_fund);
                    // if user.fund >= required_funds {
                    //     // 扣除用户资金
                    //     let mut user_model: user::ActiveModel = user.into();
                    //     user_model.fund = ActiveValue::Set(user_fund - required_funds);
                    //     // user_model.fund = user_fund - required_funds;
                    //     user_model.update(&self.db).await?;
                    let user_fund = self.avail_value(order.user_id)?;
                    if user_fund >= required_funds {
                        // Self::update_fund(&self.db, &mut self.users_cache, order.user_id, user_fund - required_funds).await?;

                        match self.open_position(
                            order.user_id,
                            &order.symbol,
                            &order.mode.unwrap(),
                            current_price,
                            order.quantity.unwrap(),
                            order.tp_price,
                            order.sp_price,
                            "long",
                            order.leverage,
                        ).await {
                            Ok(_) => {
                                Ok(())
                            }
                            Err(err) => {
                                // eprintln!("Error opening position for order {}: {}", order.order_id, err);
                                Err(err)
                            }
                        }
                    } else {
                        Err(DbErr::Custom("Insufficient funds to create market order".to_string()))
                    }
                } else {
                    Err(DbErr::RecordNotFound("User not found".to_string()))
                }
            } else if order.order_type == "close" {
                let positions = self
                    .open_positions
                    .values()
                    .filter(|p| p.user_id == order.user_id && p.is_open && p.symbol == order.symbol)
                    .cloned()
                    .collect::<Vec<_>>();

                if !positions.is_empty() {
                    assert_eq!(positions.len(), 1);
                    for position in positions {
                        self.close_position(position.position_id, current_price, if order.quantity.is_some() {
                            order.quantity.unwrap()
                        } else {
                            self.open_positions[&position.position_id].size
                        }, "closed").await?;
                    }
                    Ok(())
                } else {
                    Err(DbErr::RecordNotFound("no vault position to close market".to_string()))
                }
            } else {
                Err(DbErr::Custom("order type error".to_string()))
            }
        } else {
            Err(DbErr::Custom("create_market_order error, no such symbol".to_string()))
        }
    }

    pub fn unrealized_pnl(&self) -> f64 {
        self.open_positions.values().fold(0.0, |acc, position| {
            assert!(position.is_open);
            Self::calc_profit(position, self.all_prices[&position.symbol])
        })
    }

    pub fn orders_lock_value(&self) -> f64 {
        self.orders.values().fold(0.0, |acc, order| {
            assert!(order.is_active);
            acc + order.limit_price * order.size / order.leverage.unwrap_or(1) as f64
        })
    }

    pub fn position_value(&self, p: &vault_position::Model) -> f64 {
        let mark_price = self.all_prices[&p.symbol];
        let profit = Self::calc_profit(p, mark_price);
        profit + self.margin_required(p)
    }
    pub fn positions_lock_value(&self) -> f64 {
        self.open_positions.values().fold(0.0, |acc, p| {
            assert!(p.is_open);
            acc + self.all_prices[&p.symbol] * p.size / p.leverage.unwrap_or(1) as f64
        })
    }

    pub fn avail_value(&self, user_id: i32) -> Result<f64, DbErr> {
        if self.users_cache.contains_key(&user_id) {
            let user_fund = self.users_cache[&user_id].fund;
            Ok(user_fund - self.orders_lock_value() - self.positions_lock_value())
        } else {
            Err(DbErr::RecordNotFound("User not found".to_string()))
        }
    }

    pub fn initial_value(&self, user_id: i32) -> Result<f64, DbErr> {
        if self.users_cache.contains_key(&user_id) {
            let user_fund = self.users_cache[&user_id].fund;
            Ok(user_fund)
        } else {
            Err(DbErr::RecordNotFound("User not found".to_string()))
        }
    }
    pub fn margin_required2(position: &vault_position::Model, mark_price: f64) -> f64 {
        let leverage = position.leverage.unwrap_or(1) as f64;

        let margin_required = mark_price * position.size / leverage;
        margin_required
    }

    pub fn margin_required(&self, position: &vault_position::Model) -> f64 {
        let mark_price = self.all_prices[&position.symbol];
        let leverage = position.leverage.unwrap_or(1) as f64;

        let margin_required = mark_price * position.size / leverage;
        margin_required
    }
    pub fn maintenance_margin_required(&self, position: &vault_position::Model) -> f64 {
        let mark_price = self.all_prices[&position.symbol];
        let MAX_LEVERAGE = self.get_max_leverage(&position.symbol);
        let leverage = position.leverage.unwrap_or(1) as f64;

        let margin_required = mark_price * position.size / leverage;
        let maintenance_margin_required = margin_required * leverage / MAX_LEVERAGE / 2.0;
        let side = if position.direction == "long" { 1.0 } else { -1.0 };
        // let margin_available= if position.mode == "isolate" {
        //     margin_required - maintenance_margin_required
        // } else {
        //     self.account_value(position.user_id).unwrap() - maintenance_margin_required
        // };
        maintenance_margin_required
    }

    pub fn get_liq_price(&self, position: &vault_position::Model) -> f64 {
        let mark_price = self.all_prices[&position.symbol];
        let MAX_LEVERAGE = self.get_max_leverage(&position.symbol);
        // let margin_required =  mark_price * position.size / leverage;
        // let maintenance_margin_required = margin_required*leverage/MAX_LEVERAGE/2.0;
        let side = if position.direction == "long" { 1.0 } else { -1.0 };

        let liq_price;
        if position.mode == "isolate" {
            liq_price = mark_price - side * (self.margin_required(position) - self.maintenance_margin_required(position)) / position.size / (1.0 - (1.0 / MAX_LEVERAGE / 2.0) * side);
        } else {
            let total_maintenance_margin_required = self.open_positions.iter().fold(0.0, |acc, (_, p)| {
                acc + self.maintenance_margin_required(p)
            });
            liq_price = mark_price - side * (self.initial_value(position.user_id).unwrap() - total_maintenance_margin_required) / position.size / (1.0 - (1.0 / MAX_LEVERAGE / 2.0) * side);
        }

        liq_price
    }
    // 检查爆仓
    pub async fn check_liquidation(
        &mut self,
        symbol: &str,
        current_price: f64,
    ) -> Result<(), DbErr> {
        let users = self.open_positions.iter().map(|(_, p)| p.user_id).collect::<BTreeSet<_>>();
        for user_id in users {
            let positions = self
                .open_positions
                .values()
                .filter(|p| p.user_id == user_id && p.is_open && p.symbol == symbol)
                .collect::<Vec<_>>();

            if !positions.is_empty() {
                let position = positions[0];
                // let leverage = position.leverage.unwrap_or(1) as f64;
                let mut need_close = false;
                let side = if position.direction == "long" { 1.0 } else { -1.0 };
                let mark_price = self.all_prices[&position.symbol];

                let liq_price = self.get_liq_price(&position);
                if side > 0.0 {
                    if mark_price < liq_price {
                        need_close = true;
                    }
                } else {
                    if mark_price > liq_price {
                        need_close = true;
                    }
                }

                if need_close {
                    let positions_to_close = positions.iter().map(|p| p.position_id).collect::<Vec<_>>();
                    for position_id in positions_to_close {
                        self.close_position(position_id, current_price, self.open_positions[&position_id].size, "closed").await?;

                        // if let Some(mut sig) = signal::Entity::find()
                        //     .filter(signal::Column::VaultId.eq(user_id))
                        //     .filter(signal::Column::UnderlyingAsset.eq(symbol))
                        //     .one(&self.db).await? {
                        //     // sig.state = "closed".to_string();
                        //     let mut new_sig: signal::ActiveModel = sig.into();
                        //     new_sig.state = ActiveValue::Set("closed".to_string());
                        //     new_sig.update(&self.db).await?;
                        // }
                    }
                }
            }
        }

        Ok(())
    }
    pub async fn process_signal_statics(
        &mut self,
    ) -> Result<(), DbErr> {
        // let today :Date =  Utc::now().naive_utc().into();
        // if today > self.last_statics_date {
        if (Utc::now() - self.last_statics_time).num_seconds() >= 60*5 {
            println!("start statics signal");

            let end = Utc::now();
            let start = end - Duration::days(365);

            // let uid_coin_vec: Vec<(i32, String)> = signal::Entity::find()
            //     .select_only()                     // 仅选定列
            //     .column(signal::Column::UserId)
            //     .column(signal::Column::UnderlyingAsset)
            //     .distinct()                        // 添加 DISTINCT
            //     .into_tuple()                     // 映射为 Vec<(T1, T2)>
            //     .all(&self.db)                          // 执行查询
            //     .await?;
            //

            #[derive(Copy, Clone, Debug, EnumIter, DeriveColumn)]
            enum QueryAs {
                UserId,
                Coin,
                Time,
                ClosedPnl,
                ClosedPnlRate,
                Dir,
            }

            //
            // let records_lastyear :Vec<(i32, String, DateTimeUtc, f64, String)> = deal::Entity::find()
            //     .select_only()
            //     .column_as(deal::Column::UserId, QueryAs::UserId)
            //     .column_as(deal::Column::Coin, QueryAs::Coin)
            //     .column_as(deal::Column::Time,      QueryAs::Time)
            //     .column_as(deal::Column::ClosedPnl, QueryAs::ClosedPnl)
            //     .column_as(deal::Column::Dir, QueryAs::Dir)
            //     // .column(deal::Column::UserId)
            //     // .column(deal::Column::Time)
            //     // .column(deal::Column::ClosedPnl)
            //
            //     .filter(deal::Column::Time.gte(end - Duration::days(365)))  // 日期 ≥ start
            //     .filter(deal::Column::Time.lte(Utc::now()))    // 日期 ≤ end
            //     .into_values::<_, QueryAs>()
            //     .all(&self.db)
            //     .await?
            //     ;
            //
            // // println!("records_lastyear: {:?}", records_lastyear);
            // let mut records_lastyear_map: HashMap<(i32, String), Vec<_>> =  HashMap::new();
            // for  record in records_lastyear {
            //     records_lastyear_map.entry((record.0, record.1.clone())).or_default().push(record.clone());
            // }
            //
            let sigs = signal::Entity::find()
                .all(&self.db)
                .await?;

            let mut sigs_map: HashMap<(i32, String), (signal::Model, Vec<(String, f64)>)> =  HashMap::new();
            for  sig in sigs {
                sigs_map.insert((sig.user_id, sig.underlying_asset.clone()), (sig, vec![]));
            }
            //
            // for ((user_id, coin), records) in records_lastyear_map {
            //     if let Some((_, sig_update)) = sigs_map.get_mut(&(user_id, coin.clone())) {
            //         let mut win_count = 0;
            //         let mut total_count = 0;
            //         for val in records {
            //             if val.3 > 0.0 {
            //                 win_count += 1;
            //             }
            //             if val.4[0..5] == "close".to_string() {
            //                 total_count += 1;
            //             }
            //         }
            //
            //         let win_rate = if total_count==0 {0.0} else { win_count as f64 / total_count as f64 };
            //
            //         // println!("uid: {}, coin: {}, win rate: {}", user_id, coin, win_rate);
            //         // sig.annual_win_rate = ActiveValue::Set(win_rate);
            //         sig_update.push(("annual_win_rate".to_string(), win_rate));
            //     }
            // }

            let records_total :Vec<(i32, String, DateTimeUtc, f64, f64, String)> = deal::Entity::find()
                .select_only()
                .column_as(deal::Column::UserId, QueryAs::UserId)
                .column_as(deal::Column::Coin, QueryAs::Coin)
                .column_as(deal::Column::Time,      QueryAs::Time)
                .column_as(deal::Column::ClosedPnl, QueryAs::ClosedPnl)
                .column_as(deal::Column::ClosedPnlRate, QueryAs::ClosedPnlRate)
                .column_as(deal::Column::Dir, QueryAs::Dir)
                // .filter(deal::Column::Time.gte(end - Duration::days(30)))  // 日期 ≥ start
                // .filter(deal::Column::Time.lte(Utc::now()))    // 日期 ≤ end
                .into_values::<_, QueryAs>()
                .all(&self.db)
                .await?
                ;
            let mut records_total_map: HashMap<(i32, String), Vec<_>> =  HashMap::new();
            for  record in records_total {
                records_total_map.entry((record.0, record.1.clone())).or_default().push(record.clone());
            }
            for ((user_id, coin), records) in records_total_map {
                // let records = records.into_iter().map(|r| (r.1.clone(), r)).collect::<BTreeMap<_, _>>();

                if let Some((_, sig_update)) = sigs_map.get_mut(&(user_id, coin.clone())) {
                    let mut profit_rate_month = 1.0;
                    let mut profit_rate_3days = 1.0;
                    let mut profit_rate_7days = 1.0;
                    let mut profit_rate_cumulative = 1.0;

                    let mut win_count_1year = 0;
                    let mut total_count_1year = 0;

                    let mut win_count_history = 0;
                    let mut total_count_history = 0;
                    // historical_win_rate;
                    for val in records {
                        if Utc::now() - val.2 < Duration::days(3) {
                            profit_rate_3days *= (1.0+val.4);
                        }
                        if Utc::now() - val.2 < Duration::days(7) {
                            profit_rate_7days *= (1.0+val.4);
                        }
                        if Utc::now() - val.2 < Duration::days(30) {
                            profit_rate_month *= (1.0+val.4);
                        }
                        profit_rate_cumulative *= (1.0+val.4);

                        if Utc::now() - val.2 < Duration::days(365) {
                            if val.3 > 0.0 {
                                win_count_1year += 1;
                            }
                            if val.5 == "close".to_string() {
                                total_count_1year += 1;
                            }
                        }

                        if val.3 > 0.0 {
                            win_count_history += 1;
                        }
                        if val.5 == "close".to_string() {
                            total_count_history += 1;
                        }

                        // println!("  {} -> {:?}", dt, val);
                    }

                    let win_rate_1year = if total_count_1year ==0 {0.0} else { win_count_1year as f64 / total_count_1year as f64 };
                    let historical_win_rate = if total_count_history ==0 {0.0} else { win_count_history as f64 / total_count_history as f64 };

                    // println!("uid: {}, coin: {}, win rate: {}", user_id, coin, win_rate);
                    sig_update.push(("annual_win_rate".to_string(), win_rate_1year));
                    sig_update.push(("historical_win_rate".to_string(), historical_win_rate));

                    sig_update.push(("cumulative_income".to_string(), profit_rate_cumulative));
                    sig_update.push(("monthly_return_rate".to_string(), profit_rate_month));
                    sig_update.push(("three_yield".to_string(), profit_rate_3days));
                    sig_update.push(("seven_yield".to_string(), profit_rate_7days));
                    // println!("uid: {}, coin: {}, profit rate: {}", user_id, coin, profit_rate);
                }
            }

            for (_, (sig, updates))  in sigs_map {
                let mut sig :signal::ActiveModel = sig.clone().into();
                // let mut sig = signal::ActiveModel{
                //     ..Default::default()
                // };

                for update in updates {
                    if update.0 == "monthly_return_rate" {
                        sig.monthly_return_rate = ActiveValue::Set(update.1);
                    }
                    else if update.0 == "annual_win_rate" {
                        if update.1 >= 0.8 {
                            sig.confidence = ActiveValue::Set("high".to_string());
                        }
                        else if update.1 >= 0.5 {
                            sig.confidence = ActiveValue::Set("middle".to_string());
                        }
                        else {
                            sig.confidence = ActiveValue::Set("low".to_string());
                        }
                        sig.annual_win_rate = ActiveValue::Set(update.1);
                    }
                    else if update.0 == "historical_win_rate" {
                        sig.historical_win_rate = ActiveValue::Set(update.1);
                    }
                    else if update.0 == "seven_yield" {
                        sig.seven_yield = ActiveValue::Set(update.1);
                    }
                    else if update.0 == "three_yield" {
                        sig.three_yield = ActiveValue::Set(update.1);
                    }
                    else if update.0 == "cumulative_income" {
                        sig.cumulative_income = ActiveValue::Set(update.1);
                    }

                }


                if *sig.monthly_return_rate.as_ref() > 0.3 && *sig.monthly_alpha.as_ref() > 0.0 && *sig.annual_win_rate.as_ref() > 0.7 {
                    sig.signal_type = ActiveValue::Set("best".to_string());
                }
                else {
                    sig.signal_type = ActiveValue::Set("candidate".to_string());
                }

                if let  Some((_, position)) = self.open_positions
                    .iter()
                    .find(|(_, p)| p.user_id == *sig.user_id.as_ref() && p.symbol == *sig.underlying_asset.as_ref()) {
                    sig.signal_pnl = ActiveValue::Set(Self::calc_profit(position, self.all_prices[&position.symbol]));
                    // println!("uid: {}, coin: {}, pnl: {}", *sig.user_id.as_ref(), *sig.underlying_asset.as_ref(), *sig.signal_pnl.as_ref());
                }
                sig.updated_date = ActiveValue::Set(Some(Utc::now()));
                // let new_sig :signal::ActiveModel = sig.into();
                let _ = sig.clone().update(&self.db).await?;
            }

            self.last_statics_time = Utc::now();
        }

        Ok(())
    }

    pub async fn update_leverage(&mut self, user_id: i32, symbol: &str, leverage: i32) -> Result<(), DbErr> {
        assert!(self.avail_value(user_id)? >= 0.0);

        let old_leverage;
        {
            let mut pos_vec = self.open_positions.iter_mut().filter(|(_, p)| p.user_id == user_id && p.symbol == symbol).collect::<Vec<_>>();
            if pos_vec.is_empty() {
                return Err(DbErr::RecordNotFound("Position not found".to_string()));
            }
            assert_eq!(pos_vec.len(), 1);
            let position = &mut *(pos_vec[0].1);

            old_leverage = position.leverage;
            position.leverage = Some(leverage);
        }

        if self.avail_value(user_id)? < 0.0 {
            let mut pos_vec = self.open_positions.iter_mut().filter(|(_, p)| p.user_id == user_id && p.symbol == symbol).collect::<Vec<_>>();
            assert_eq!(pos_vec.len(), 1);
            let position = &mut *(pos_vec[0].1);

            position.leverage = old_leverage;

            Err(DbErr::RecordNotFound("User not found".to_string()))
        } else {
            Ok(())
        }
    }

    pub async fn create_signal(&mut self, param: SignalCreate) -> Result<signal::Model, DbErr> {
        if self.open_positions.iter().find(|(_, p)| p.user_id == param.user_id).is_some() {
            return Err(DbErr::Custom("Position already exist".to_string()));
        }
        if !self.users_cache.contains_key(&param.user_id) {
            return Err(DbErr::RecordNotFound("User not found".to_string()));
        }
        if signal::Entity::find()
            .filter(signal::Column::UserId.eq(param.user_id))
            .filter(signal::Column::UnderlyingAsset.eq(param.underlying_asset.clone()))
            .count(&self.db).await? > 0 {
            return Err(DbErr::Custom("signal to asset is already exist".to_string()));
        }

        // let user = &self.users_cache[&param.user_id];
        let new_signal = signal::ActiveModel {
            // id: ActiveValue::Set(param.id),
            username: ActiveValue::Set(param.username),
            uuid: ActiveValue::Set(param.uuid),
            signal_type: ActiveValue::Set("candidate".to_string()),
            signal_name: ActiveValue::Set(param.signal_name),
            signal_pnl: ActiveValue::Set(0.0),
            running_status: ActiveValue::Set("closed".to_string()),

            consecutive_wins: ActiveValue::Set(0),
            subscribers: ActiveValue::Set(0),
            operation_direction: ActiveValue::Set("unknown".to_string()),
            underlying_asset: ActiveValue::Set(param.underlying_asset.clone()),

            monthly_return_rate: ActiveValue::Set(1.0),
            monthly_alpha: ActiveValue::Set(0.0),
            annual_win_rate: ActiveValue::Set(0.0),
            cumulative_income: ActiveValue::Set(0.0),

            sharpe_ratio: ActiveValue::Set(0.0),
            three_yield: ActiveValue::Set(0.0),
            seven_yield: ActiveValue::Set(0.0),
            max_drawdown_7days: ActiveValue::Set(0.0),
            running_time: ActiveValue::Set(0.0),
            historical_win_rate: ActiveValue::Set(0.0),
            profit_loss_count: ActiveValue::Set(0.0),
            profit_loss_ratio: ActiveValue::Set(0.0),
            confidence: ActiveValue::Set("low".to_string()),  // high, middle, low,
            evaluation_status: ActiveValue::Set("unqualified".to_string()), // qualified, unqualified, qualifying
            review: ActiveValue::Set("".to_string()),

            user_id: ActiveValue::Set(param.user_id),
            created_date: ActiveValue::Set(Some(Utc::now())),
            ..Default::default()
        };


        let sig = new_signal.insert(&self.db).await?;
        Ok(sig)
    }
    // pub async fn signal_generate(&mut self, param: SignalGenerate) -> Result<(), DbErr> {
    //
    //     if signal::Entity::find()
    //         .filter(signal::Column::VaultId.eq(param.vault_id))
    //         .filter(signal::Column::UnderlyingAsset.eq(param.underlying_asset.clone()))
    //         .filter(signal::Column::State.is_in(["initial", "closing", "running"]))
    //         .count(&self.db).await? > 0 {
    //         return Err(DbErr::Custom("signal to asset is already exist".to_string()));
    //     }
    //
    //     let new_signal = signal::ActiveModel {
    //         id: ActiveValue::Set(param.id),
    //         username: ActiveValue::Set(param.username),
    //         uuid: ActiveValue::Set(param.uuid),
    //         signal_name: ActiveValue::Set(param.signal_name),
    //         operation_direction: ActiveValue::Set(param.operation_direction.clone()),
    //         underlying_asset: ActiveValue::Set(param.underlying_asset.clone()),
    //         vault_id: ActiveValue::Set(param.vault_id),
    //
    //         mode: ActiveValue::Set(param.mode.clone()),
    //
    //         price: ActiveValue::Set(param.price),
    //         size: ActiveValue::Set(param.size),
    //
    //         state: ActiveValue::Set("initial".to_string()),
    //
    //         tp_price: ActiveValue::Set(param.tp_price),
    //         sp_price: ActiveValue::Set(param.sp_price),
    //
    //         leverage: ActiveValue::Set(param.leverage),
    //
    //         ..Default::default()
    //     };
    //
    //
    //     let sig = new_signal.insert(&self.db).await?;
    //
    //     {
    //         let backend = self.db.get_database_backend();
    //         // let schema = Schema::new(backend);
    //
    //         // let mut tab =  Table::create();
    //         // 构造建表语句，设置动态表名
    //         // let mut create_stmt = tab
    //         //     .table(Alias::new(format!("signal_statics-{}", sig.sig_id)))
    //         //     .if_not_exists();
    //         // create_stmt.col(&mut schema.get_column_def::<signal_statics::Entity>(signal_statics::Column::Date));
    //         // create_stmt.col(&mut schema.get_column_def::<signal_statics::Entity>(signal_statics::Column::PositionValue));
    //         // // 指定主键约束
    //         // create_stmt.primary_key(Index::create().col(signal_statics::Column::Date).primary());
    //         // // 生成 SQL 并执行
    //         // self.db.execute(backend.build(create_stmt)).await?;
    //
    //         let mut stmt = Schema::new(backend)
    //             .create_table_from_entity(signal_statics::Entity);  // :contentReference[oaicite:9]{index=9}
    //
    //         // 3. 动态覆盖表名
    //         stmt.table(Alias::new(format!("signal_statics-{}", sig.sig_id)));         // :contentReference[oaicite:10]{index=10}
    //         self.db.execute(backend.build(&stmt)).await?;
    //     }
    //
    //     let order = LimitOrder {
    //         user_id: param.vault_id,
    //         symbol: param.underlying_asset.clone(),
    //         order_type: "open".to_string(),
    //         quantity: Some(param.size),
    //         limit_price: Some(param.price),
    //         order_action_type: Some(param.operation_direction),
    //         mode: Some(param.mode),
    //         leverage: param.leverage,
    //         tp_price: param.sp_price,
    //         sp_price: param.tp_price,
    //         strategy_id: Some(sig.sig_id),
    //     };
    //
    //     // {
    //     //     if let Some(mut sig) = signal::Entity::find_by_id(order.signal_id.unwrap()).one(&self.db).await? {
    //     //
    //     //         // let sig = signal::Entity::find()
    //     //         // .filter(signal::Column::VaultId.eq(2))
    //     //         // .filter(signal::Column::UnderlyingAsset.eq("BTC"))
    //     //         // .one(&self.db).await?;
    //     //     // if let Some(sig) = sig.as_ref() {
    //     //         println!("{:?}", sig);
    //     //     }
    //     //     else {
    //     //         assert!(false);
    //     //     }
    //     //     std::process::exit(0);
    //     // }
    //
    //     self.create_limit_order(order).await?;
    //     Ok(())
    // }
    // pub async fn signal_cancel(&mut self, param: SignalCancel) -> Result<(), DbErr> {
    //     let sigs = signal::Entity::find()
    //         .filter(signal::Column::VaultId.eq(param.vault_id))
    //         .filter(signal::Column::UnderlyingAsset.eq(param.underlying_asset.clone()))
    //         .filter(signal::Column::State.is_in(["initial"]))
    //         .all(&self.db).await?;
    //
    //     for  sig in sigs {
    //         if let Some((order_id, _)) = self.orders.iter().find(|(_, o)| o.signal_id.is_some() && o.signal_id.unwrap() == sig.sig_id) {
    //             self.cancel_order(*order_id).await?;
    //         }
    //
    //         let backend = self.db.get_database_backend();
    //         // 构造 DROP TABLE 语句
    //         let drop_stmt = Table::drop()
    //             .if_exists()                  // 可选：添加 IF EXISTS
    //             .table(Alias::new(format!("signal_statics-{}", sig.sig_id)))
    //             .to_owned();
    //         // 执行
    //         self.db.execute(backend.build(&drop_stmt)).await?;
    //     }
    //
    //     signal::Entity::delete_many()
    //         .filter(signal::Column::VaultId.eq(param.vault_id))
    //         .filter(signal::Column::UnderlyingAsset.eq(param.underlying_asset))
    //         .filter(signal::Column::State.eq("initial".to_string()))
    //         .exec(&self.db).await?;
    //
    //     Ok(())
    // }
    //
    // pub async fn signal_closeposition(&mut self, param: SignalClosePosition) -> Result<(), DbErr> {
    //     let sig = signal::Entity::find()
    //         .filter(signal::Column::VaultId.eq(param.vault_id))
    //         .filter(signal::Column::UnderlyingAsset.eq(param.underlying_asset.clone()))
    //         .filter(signal::Column::State.is_in(["closing", "running"]))
    //         .one(&self.db).await?;
    //     if sig.is_none() {
    //         return Err(DbErr::Custom("signal not found".to_string()));
    //     }
    //     let mut sig = sig.unwrap();
    //     if let Some((order_id, _)) = self.orders.iter().find(|(_, o)| o.signal_id.is_some() && o.signal_id.unwrap() == sig.sig_id) {
    //         self.cancel_order(*order_id).await?;
    //     }
    //
    //     if param.price.is_some() {
    //         let order = LimitOrder {
    //             user_id: param.vault_id,
    //             symbol: param.underlying_asset.clone(),
    //             order_type: "close".to_string(),
    //             limit_price: param.price,
    //             quantity: None,
    //             ..Default::default()
    //         };
    //         self.create_limit_order(order).await?;
    //
    //         let mut new_sig: signal::ActiveModel = sig.into();
    //         new_sig.state = ActiveValue::Set("closing".to_string());
    //         new_sig.update(&self.db).await?;
    //     } else {
    //         // let position = vault_position::Entity::find()
    //         //     .filter(vault_position::Column::UserId.eq(param.vault_id))
    //         //     .filter(vault_position::Column::Symbol.eq(param.underlying_asset.clone()))
    //         //     .one(&self.db).await?.unwrap();
    //         //
    //         // self.close_position(position.position_id, self.all_prices[&position.symbol], position.size).await?;
    //
    //         let order = MarketOrder {
    //             user_id: param.vault_id,
    //             symbol: param.underlying_asset.clone(),
    //             order_type: "close".to_string(),
    //             quantity: None,
    //             ..Default::default()
    //         };
    //         self.create_market_order(order).await?;
    //
    //         // sig.state = "closed".to_string();
    //         let mut new_sig: signal::ActiveModel = sig.into();
    //         new_sig.state = ActiveValue::Set("closed".to_string());
    //         new_sig.update(&self.db).await?;
    //     }
    //     Ok(())
    // }
    //
    // pub async fn signal_list(&mut self) -> Result<Vec<signal::Model>, DbErr> {
    //     let sig_list = signal::Entity::find().all(&self.db).await?;
    //
    //     Ok(sig_list)
    // }
}
// 处理添加市价单的 API 接口
// #[post("/market-order")]
// async fn add_market_order(
//     tx: web::Data<mpsc::Sender<PositionOperation>>,
//     order: web::Json<MarketOrder>,
//     current_price: web::Data<BigDecimal>,
// ) -> impl Responder {
//     let market_order = order.into_inner();
//     let current_price = *current_price.get_ref();
//     if let Err(e) = tx.send(PositionOperation::ProcessMarketOrder {
//         market_order,
//         current_price,
//     }).await {
//         return HttpResponse::InternalServerError().body(format!("Error sending message: {}", e));
//     }
//     HttpResponse::Ok().body("Market order processed successfully")
// }

fn dberr_to_globalerr(value: hypertrader_dbs::DbErr) -> GlobalError {
    GlobalError {
        code: hypertrader_utils::apicodes::errors::ERROR_CODES::common::DbError,
        msg: value.to_string().into(),
    }
}


#[handler]
pub async fn get_test_api(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        "OK, API is running",
    )));

    Ok(())
}

#[handler]
pub async fn get_all_users(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let cs = cs.lock().await;
    let db = &cs.db;
    let all_users :Vec<user::Model> = user::Entity::find().all(db).await.map_err(|e|dberr_to_globalerr(e))?;

#[derive(Serialize)]
    pub struct UserInfo {
        basic: user::Model,
        avail_value:   f64,
        unrealized_pnl: f64,
    }

    let all_users = all_users.into_iter().map(|u| UserInfo {
        basic: u.clone(),
        avail_value: cs.avail_value(u.id).unwrap(),
        unrealized_pnl: cs.unrealized_pnl(),
    }).collect::<Vec<_>>();

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        all_users,
    )));

    Ok(())
}

#[handler]
pub async fn get_all_mids(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let all_mids :HashMap<String, f64> = cs.lock().await.all_prices.clone();
    let all_mids = all_mids.iter().filter(|(coin,_)|!coin.contains("@")).collect::<std::collections::BTreeMap<_, _>>();
    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        all_mids,
    )));

    Ok(())
}

#[handler]
pub async fn get_active_orders(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    // let config = depot.obtain::<Config>().unwrap();
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let all_orders = cs.lock().await.orders.clone();
    let active_orders = all_orders.iter().filter(|(_,p)|p.is_active).collect::<std::collections::BTreeMap<_, _>>();

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        active_orders,
    )));

    Ok(())
}

#[handler]
pub async fn get_active_position(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    // let config = depot.obtain::<Config>().unwrap();
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    #[derive(Serialize)]
    struct Position {
        basic: vault_position::Model,
        margin_required:  f64,
        maintenance_margin_required: f64,
        liq_price: f64,
        unrealized_pnl: f64,
    }
    let cs = cs.lock().await;
    let all_positions = cs.open_positions.clone();
    let active_positions = all_positions.iter()
        .filter(|(_,p)|p.is_open)
        .map(|(i, p)| (
            i,
            Position {
                basic: p.clone(),
                margin_required: cs.margin_required(p),
                maintenance_margin_required: cs.maintenance_margin_required(p),
                liq_price: cs.get_liq_price(p),
                unrealized_pnl: ContractService::calc_profit(p, cs.all_prices[&p.symbol]),
            }
        ))
        .collect::<std::collections::BTreeMap<_, _>>();

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        active_positions,
    )));

    Ok(())
}

#[handler]
pub async fn get_signals(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    // let config = depot.obtain::<Config>().unwrap();
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;
    let cs = cs.lock().await;

    let sigs = signal::Entity::find().all(&cs.db).await.map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        sigs,
    )));

    Ok(())
}

#[derive(serde::Deserialize, Debug)]
pub struct QueryActivePosProfit {
    pub user_id: i32,
    pub symbol: Option<String>,
}

#[handler]
pub async fn get_active_position_profit(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    // let config = depot.obtain::<Config>().unwrap();
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let req = req
        .parse_body::<QueryActivePosProfit>()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;

    // let all_positions = cs.lock().await.open_positions.clone();
    // let active_positions = all_positions
    //     .iter().filter(|(_,p)|p.is_open && p.user_id == req.user_id && (!req.symbol.is_some() || p.symbol == *req.symbol.as_ref().unwrap())).collect::<Vec<_>>();
    // let mut profit = 0.0;
    // let all_mids = cs.lock().await.all_prices.clone();
    // for (_, position) in active_positions {
    //         let current_price = all_mids.get(position.symbol.as_str()).unwrap();
    //         profit += ContractService::calc_profit(&position, *current_price);
    //     }
    // }
    let profit = 0.0;
    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        profit,
    )));

    Ok(())
}

#[handler]
pub async fn post_create_limit_order(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let req = req
        .parse_body::<LimitOrder>()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;

    // println!("get req: {:?}", req);

    let order = cs.lock().await.create_limit_order(req).await.map_err(|e|dberr_to_globalerr(e))?;

    #[derive(Serialize)]
    struct OrderId {
        order_id: i32
    }
    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        OrderId {
            order_id: order.order_id
        }
    )));

    Ok(())
}

#[handler]
pub async fn post_cancel_order(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    #[derive(Deserialize)]
    struct CancelOrder {
        order_id: i32,
    }
    let req = req
        .parse_body::<CancelOrder>()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;

    // println!("get req: {:?}", req);

    cs.lock().await.cancel_order(req.order_id).await.map_err(|e|dberr_to_globalerr(e))?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        ""
    )));

    Ok(())
}

#[handler]
pub async fn post_create_market_order(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let req = req
        .parse_body::<MarketOrder>()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;

    // println!("get req: {:?}", req);

    cs.lock().await.create_market_order(req).await.map_err(|e|dberr_to_globalerr(e))?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        ""
    )));

    Ok(())
}

#[handler]
pub async fn post_update_leverage(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    // let config = depot.obtain::<Config>().unwrap();
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    #[derive(Deserialize)]
    struct UpdateLeverage {
        pub user_id:  i32,
        pub symbol:   String,
        pub leverage: i32,
    }
    let req = req
        .parse_body::<UpdateLeverage>()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;

    cs.lock().await.update_leverage(req.user_id, &req.symbol, req.leverage).await.map_err(|e|dberr_to_globalerr(e))?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        "",
    )));

    Ok(())
}


#[handler]
pub async fn post_create_signal(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let cs = depot
        .obtain::<Arc::<Mutex<ContractService>>>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let req = req
        .parse_body::<SignalCreate>()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;

    // println!("get req: {:?}", req);

    let sig = cs.lock().await.create_signal(req).await.map_err(|e|dberr_to_globalerr(e))?;

    #[derive(Serialize)]
    struct SignalID {
        signal_id: i32
    }

    let sig_id = SignalID {
        signal_id: sig.id,
    };

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        sig_id
    )));

    Ok(())
}

// #[handler]
// pub async fn post_signal_generate(
//     req: &mut Request,
//     depot: &mut Depot,
//     res: &mut Response,
//     _ctrl: &mut FlowCtrl,
// ) -> Result<(), GlobalError> {
//     let cs = depot
//         .obtain::<Arc::<Mutex<ContractService>>>()
//         .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;
//
//     let req = req
//         .parse_body::<SignalGenerate>()
//         .await
//         .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;
//
//
//     cs.lock().await.signal_generate(req).await.map_err(|e|dberr_to_globalerr(e))?;
//
//     res.status_code(StatusCode::OK);
//     res.render(Json(GlobalSuccess::new_json(
//         SUCCESS_CODES::test::TestSuccess,
//         ""
//     )));
//
//     Ok(())
// }
//
// #[handler]
// pub async fn post_signal_cancel(
//     req: &mut Request,
//     depot: &mut Depot,
//     res: &mut Response,
//     _ctrl: &mut FlowCtrl,
// ) -> Result<(), GlobalError> {
//     let cs = depot
//         .obtain::<Arc::<Mutex<ContractService>>>()
//         .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;
//
//     let req = req
//         .parse_body::<SignalCancel>()
//         .await
//         .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;
//
//
//     cs.lock().await.signal_cancel(req).await.map_err(|e|dberr_to_globalerr(e))?;
//
//     res.status_code(StatusCode::OK);
//     res.render(Json(GlobalSuccess::new_json(
//         SUCCESS_CODES::test::TestSuccess,
//         ""
//     )));
//
//     Ok(())
// }
//
// #[handler]
// pub async fn post_signal_closeposition(
//     req: &mut Request,
//     depot: &mut Depot,
//     res: &mut Response,
//     _ctrl: &mut FlowCtrl,
// ) -> Result<(), GlobalError> {
//     let cs = depot
//         .obtain::<Arc::<Mutex<ContractService>>>()
//         .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;
//
//     let req = req
//         .parse_body::<SignalClosePosition>()
//         .await
//         .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;
//
//     cs.lock().await.signal_closeposition(req).await.map_err(|e|dberr_to_globalerr(e))?;
//
//     res.status_code(StatusCode::OK);
//     res.render(Json(GlobalSuccess::new_json(
//         SUCCESS_CODES::test::TestSuccess,
//         ""
//     )));
//
//     Ok(())
// }
//
// #[handler]
// pub async fn get_signal_list(
//     req: &mut Request,
//     depot: &mut Depot,
//     res: &mut Response,
//     _ctrl: &mut FlowCtrl,
// ) -> Result<(), GlobalError> {
//     let cs = depot
//         .obtain::<Arc::<Mutex<ContractService>>>()
//         .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;
//
//     let sig_list = cs.lock().await.signal_list().await.map_err(|e|dberr_to_globalerr(e))?;
//
//     res.status_code(StatusCode::OK);
//     res.render(Json(GlobalSuccess::new_json(
//         SUCCESS_CODES::test::TestSuccess,
//         sig_list
//     )));
//
//     Ok(())
// }
