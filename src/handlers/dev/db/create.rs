// use hypertrader_dbs::utils::user::UserManager;
use state::AppState;

use super::*;

#[derive(serde::Deserialize, serde::Serialize)]
pub struct CreateUserRequest {
    name: String,
    email: String,
}

#[handler]
pub async fn post_create_user(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    _ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    // let config = depot.obtain::<Config>().unwrap();
    let app_state = depot
        .obtain::<AppState>()
        .map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

    let req = req
        .parse_body::<CreateUserRequest>()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to parse request body: {}", e))?;

    let user_manager = UserManager::new(app_state.db.clone());
    let user = user_manager.create_user(req.name, req.email).await?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::test::TestSuccess,
        user,
    )));

    Ok(())
}
