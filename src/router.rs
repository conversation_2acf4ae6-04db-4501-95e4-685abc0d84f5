use std::collections::HashMap;
use std::str::FromStr;
use std::sync::Arc;
use std::thread;
use std::time::Duration;
use chrono::Utc;
use reqwest::StatusCode;
use crate::handlers::dev::{db, service::contract_service};
use crate::handlers::{dev, monitor, state::AppState, ws::ws_handler};
use salvo::{handler, Request, Response, Router};
use salvo::affix_state;
use salvo::extract::metadata::SourceParser::Json;
use salvo::prelude::Text;
use tokio::task;
use tokio::time::sleep;
use hypertrader_dbs::{DatabaseConnection, DbErr};
// use hypertrader_dbs::utils::order::OrderManager;
// use hypertrader_dbs::utils::user::UserManager;
// use hypertrader_dbs::utils::vault_position::VaultPositionManager;
use hypertrader_utils::apicodes::errors::GlobalError;
// use crate::handlers::ws::{broadcast, WsMessage, WS_MANAGER};

// mod contract_service;
use hypertrader_dbs::schemas::{user, vault_position};
use crate::handlers::dev::service::contract_service::{ContractService, MarketOrder};
use hyperliquid_rust_sdk::{AllMids, BaseUrl, InfoClient, MarketOrderParams, Subscription};
use stdext::default::default;
use tokio::sync::mpsc::unbounded_channel;
use tokio::sync::Mutex;
use tracing_subscriber::util::SubscriberInitExt;
use clickhouse::{Client, Row};

// 定义持仓数据结构体
#[derive(Debug)]
struct PositionData {
    symbol: String,
    quantity: u32,
    price: f64,
}

// 定义下单数据结构体
#[derive(Debug)]
struct OrderData {
    symbol: String,
    order_type: String,
    quantity: u32,
    price: f64,
}

// 定义包含多种消息类型的枚举
#[derive(Debug)]
enum Message {
    Position(PositionData),
    Order(OrderData),
}

// 获取所有订单
#[handler]
async fn get_all_orders(req: &mut Request, res: &mut Response) -> Result<(),GlobalError>  {
    let app_state = AppState::new().await;
    // let orders = order::find().all(app_state.db.as_ref()).await;
    // match orders {
    //     Ok(orders) => res.render(Json(orders)),
    //     Err(err) => {
    //         res.status_code(StatusCode::INTERNAL_SERVER_ERROR);
    //         res.render(Text::Plain(format!("获取订单失败: {}", err)));
    //     }
    // }
    Ok(())
}

pub async fn router() -> Router {
    let app_state = AppState::new().await;
    let clickhouse_client =  app_state.hyperliquid_data_manager.kline_manager.clickhouse_client.clone();

    if false
    {
        #[derive(Row, serde::Deserialize, Debug)]
        struct KlineRow {
            symbol: String,
            // interval: String,
            time_open: u64,
            time_close: u64,
            open: f64,
            high: f64,
            low: f64,
            close: f64,
            total_volume: f64,
            num_trades: u64,
        }

        // let get_price_query =
        //     "OPTIMIZE TABLE default.ohlc_1h FINAL; -- Perform finalizeAggregation to get latest row value
        //     SELECT
        //         time_open,
        //         time_close,
        //         symbol,
        //         finalizeAggregation(open) AS open_price,
        //         high AS high_price,
        //         low AS low_price,
        //         finalizeAggregation(close) AS close_price,
        //         (total_volume) AS trade_volume,
        //         (num_trades) AS num_trades,
        //         toYYYYMM(time_open) AS month_partition
        //     FROM default.ohlc_1h -- Change you time frame you want to get OHLC prices
        //     WHERE symbol = 'BTC'
        //     ORDER BY time_open DESC
        //     --        , symbol ASC
        //     LIMIT 1000;  -- Adding limit for performance reasons";
        let get_price_query =
            "SELECT symbol, time_open, time_close, `open`, high, low, `close`, total_volume, num_trades FROM `default`.ohlc_1m \
            where time_open == '2025-05-20 11:17:00'\
            ";

        let client = clickhouse_client.client.lock().await;
        // let mut cursor = client.query(&get_price_query).fetch::<KlineRow>().expect("read failed ");
        // let mut results = Vec::new();
        // while let Some(row) = cursor.next().await.expect("read failed") {
        //     println!("row:  {:?}", row);
        //     results.push(row);
        // }

        let mut cursor = client.query(&get_price_query).fetch::<KlineRow>().unwrap();
        while let Ok(Some(rec)) = cursor.next().await {
            // let name: String = row.get("open")?;
            // println!("{}: {}", id, name);
            println!("row: {:?}", rec);
        }
        std::process::exit(0);
    }
    
    let db_arc = Arc::new(app_state.db.clone());

    // crate::handlers::dev::service::contract_service::ContractService;
    let mut contract_service = ContractService{
        db: app_state.db.clone(),
        open_positions: HashMap::new(),
        orders: HashMap::new(),
        users_cache: Default::default(),
        all_prices: Default::default(),
        max_leverage:  HashMap::from([
            ("ETH".to_string(), 25),
            ("BTC".to_string(), 40),
        ]),
        // last_statics_date: Utc::now().naive_utc().into(),
        last_statics_time: Utc::now()- chrono::Duration::seconds(30),

    };

    // let last_date = Utc::now() - Duration::from_secs(3600*24); 
    // contract_service.last_statics_date = last_date.naive_utc().into();
    
    // println!("<UNK>: {}", contract_service.last_statics_date);
    contract_service.load_from_db().await;
    
    let contract_service = Arc::new(Mutex::new(contract_service));
    
    let contract_service2 = contract_service.clone();
    // 启动价格监测任务
    tokio::spawn(async move {

        let mut info_client = InfoClient::new(None, Some(BaseUrl::Testnet), None).await.unwrap();

        let (sender, mut receiver) = unbounded_channel();
        let subscription_id = info_client
            .subscribe(Subscription::AllMids, sender)
            .await
            .unwrap();

        // tokio::spawn(async move {
        //     sleep(Duration::from_secs(30)).await;
        //     println!("Unsubscribing from mids data");
        //     info_client.unsubscribe(subscription_id).await.unwrap()
        // });

        // This loop ends when we unsubscribe
        while let Some(hyperliquid_rust_sdk::Message::AllMids(all_mids)) = receiver.recv().await {
            println!("get all mids num: {}, valid num: {}, BTC: {}, ETH: {}",
                     all_mids.data.mids.len(), all_mids.data.mids.iter().filter(|(coin,_)|!coin.contains("@")).count(),
                     all_mids.data.mids["BTC"],
                     all_mids.data.mids["ETH"],
            );

            let mut all_prices = HashMap::new();
            for (symbol, current_price_str) in &all_mids.data.mids {
                if symbol.contains("@") {
                    continue;
                }

                if let Ok(current_price) = f64::from_str(&current_price_str) {
                    all_prices.insert(symbol.clone(), current_price);
                }
            }
            contract_service.lock().await.all_prices = all_prices.clone();

            for (symbol, &current_price) in &all_prices
            {
                let mut contract_service = contract_service.lock().await;
                // println!("get coin: {}, price: {}", coin_name, current_price);
                contract_service.process_limit_orders(&symbol, current_price).await.expect("process_limit_orders error");
                contract_service.process_stop_orders(&symbol, current_price).await.expect("process_stop_orders error");
                contract_service.check_liquidation(&symbol, current_price).await.expect("check_liquidation error");
            }


            let mut contract_service = contract_service.lock().await;

            contract_service.process_signal_statics().await.expect("process_signal_statics error");

        }

        return ;
    });

    Router::with_path("/")
        .get(crate::router::contract_service::get_test_api)
        .push(
    Router::with_path("apis/v0")
        .get(crate::router::contract_service::get_test_api)
        .hoop(affix_state::inject(app_state))
        .hoop(affix_state::inject(contract_service2))
        .push(
            Router::with_path("dev")
                .push(
                    Router::with_path("db")
                        // .push(Router::with_path("create").goal(db::create::post_create_user))
                    // .push(Router::with_path("create_strategy").goal(db::strategy::create_strategy))
                    // .push(Router::with_path("create_order").goal(db::order::create_order))
                    // .push(Router::with_path("update_order").goal(db::order::update_order))
                    // .push(Router::with_path("create_signal").goal(db::signal::create_signal)),
                )
                .push(Router::with_path("parse_error").goal(dev::parse_error))
                .push(Router::with_path("success").goal(dev::success))
                .push(
                    Router::with_path("cs")
                        .push(Router::with_path("get_all_users").get(crate::router::contract_service::get_all_users))
                        .push(Router::with_path("get_all_mids").get(crate::router::contract_service::get_all_mids))
                        .push(Router::with_path("get_active_orders").get(crate::router::contract_service::get_active_orders))
                        .push(Router::with_path("get_active_position").get(crate::router::contract_service::get_active_position))
                        .push(Router::with_path("get_active_position_profit").get(crate::router::contract_service::get_active_position_profit))
                        .push(Router::with_path("get_signals").get(crate::router::contract_service::get_signals))

                        .push(Router::with_path("post_create_limit_order").post(crate::router::contract_service::post_create_limit_order))
                        .push(Router::with_path("post_cancel_order").post(crate::router::contract_service::post_cancel_order))
                        .push(Router::with_path("post_create_market_order").post(crate::router::contract_service::post_create_market_order))
                        .push(Router::with_path("post_update_leverage").post(crate::router::contract_service::post_update_leverage))
                        .push(Router::with_path("post_create_signal").post(crate::router::contract_service::post_create_signal))

                )
                .push(Router::with_path("parse_error").goal(dev::parse_error))
                .push(Router::with_path("success").goal(dev::success)),
        )
        .push(Router::with_path("get_all_orders").goal(get_all_orders))
        .push(Router::with_path("ws").goal(ws_handler))
        .push(
            Router::with_path("monitor").push(
                Router::with_path("address")
                    .push(
                        Router::with_path("subscribe")
                            .goal(monitor::address::post_subscribe_address),
                    )
                    .push(
                        Router::with_path("unsubscribe")
                            .goal(monitor::address::unsubscribe::post_unsubscribe_address),
                    )
                    .push(
                        Router::with_path("list")
                            .goal(monitor::address::list::get_list_subscribed_addresses),
                    ),
            ),
        )
    )
        // .push(
        //     Router::with_path("signal")
        //         .push(Router::with_path("generate").post(contract_service::post_signal_generate))
        //         .push(Router::with_path("cancel").post(contract_service::post_signal_cancel))
        //         .push(Router::with_path("closeposition").post(contract_service::post_signal_closeposition))
        //         .push(Router::with_path("list").get(contract_service::get_signal_list))
        // 
        // )

}
