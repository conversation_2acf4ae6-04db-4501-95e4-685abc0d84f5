[package]
name = "hypertrader-data"
version = "0.1.0"
edition = "2024"

[dependencies]
hypertrader-utils = { workspace = true }
hyperliquid_rust_sdk = { workspace = true }
hypertrader-dbs = { workspace = true }
hypertrader-clickhouse = { workspace = true }
hypertrader-hyperliquid = { workspace = true }
hypertrader-models = { workspace = true }

anyhow = { workspace = true }

dashmap = { workspace = true }
tracing = { workspace = true }
tokio = { workspace = true, features = ["full"] }

serde_json = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
futures.workspace = true
kanal = { version = "0.1.1", features = ["async"] }
strum = { version = "0.27.1", features = ["derive"] }
strum_macros = "0.27.1"
