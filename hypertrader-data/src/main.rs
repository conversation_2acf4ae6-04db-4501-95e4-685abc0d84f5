// use hypertrader_dbs::init_db;
use hypertrader_models::kline::interval::KlineInterval;
use hypertrader_utils::init::ensure_inited;

use hypertrader_data::cex::hyperliquid::HyperliquidDataManager;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    // let db = init_db().await?;
    let hyperliquid_data_manager: HyperliquidDataManager =
        HyperliquidDataManager::new_without_postgres(false).await?;
    hyperliquid_data_manager.init().await?;

    let subscribed_coins = hyperliquid_data_manager
        .kline_manager
        .get_subscribed_coins_limit(5);

    hyperliquid_data_manager
        .kline_manager
        .set_subscribed_coins(subscribed_coins);

    hyperliquid_data_manager.kline_manager.run().await?;

    let durations = KlineInterval::all();
    for duration in durations {
        hyperliquid_data_manager
            .kline_manager
            .fetch_subscribed_coins_klines(&duration, 8)
            .await?;
    }

    hyperliquid_data_manager
        .kline_manager
        .clickhouse_client
        .optimize_table()
        .await?;

    // let subscribed_coins = hyperliquid_data_manager
    //     .kline_manager
    //     .get_subscribed_coins();

    // println!("subscribed_coins: {:#?}", subscribed_coins);

    // // hyperliquid_data_manager
    // //     .kline_manager
    // //     .fetch_and_store_klines_all("ETH", &KlineInterval::OneMinute)
    // //     .await?;

    // hyperliquid_data_manager
    //     .kline_manager
    //     .fetch_and_store_klines_all("ETH", KlineInterval::OneMinute)
    //     .await?;

    // let klines = hyperliquid_data_manager
    //     .client
    //     .klines_history(
    //         "ETH".to_string(),
    //         0,
    //         2690569402225,
    //         KlineInterval::OneMinute,
    //     )
    //     .await?;

    // let start_at = klines[0].time_open;
    // let start_at_str = chrono::DateTime::from_timestamp_millis(start_at as i64)
    //     .unwrap()
    //     .to_rfc3339();

    // let end_at = klines[klines.len() - 1].time_open;
    // let end_at_str = chrono::DateTime::from_timestamp_millis(end_at as i64)
    //     .unwrap()
    //     .to_rfc3339();

    // println!("start_at_str: {:?}", start_at_str);
    // println!("end_at_str: {:?}", end_at_str);
    // println!("total klines: {}", klines.len());

    // println!("klines: {:#?}", klines[0]);

    // println!("klines: {klines:?}");

    // let meta = hyperliquid_data_manager.client.meta().await;
    // let mut coin_to_asset = std::collections::HashMap::new();
    // for (asset_ind, asset) in meta.universe.iter().enumerate() { d
    //     coin_to_asset.insert(asset.name.clone(), asset_ind as u32);
    // }

    // println!("coin_to_asset: {coin_to_asset:?}");

    // let user = H160::from_str(&std::env::var("HYPER_ADDRESS").unwrap()).unwrap();
    // let user = H160::from_str("******************************************").unwrap();
    // hyperliquid_data_manager.subscribe_user(user).await?;

    // // vault balance sub
    // // ******************************************
    // let user = H160::from_str("******************************************").unwrap();
    // hyperliquid_data_manager.subscribe_user(user).await?;

    // hyperliquid_data_manager
    //     .kline_manager
    //     .client
    //     .subscribe_coin("ETH".to_string(), KlineInterval::OneMinute)
    //     .await?;

    tokio::time::sleep(std::time::Duration::from_secs(30000)).await;

    Ok(())
}

#[test]
fn test_ts() {
    let t1 = chrono::Utc::now().timestamp();
    let t2 = chrono::Utc::now().timestamp_millis();
    println!("t1: {}", t1);
    println!("t2: {}", t2);

    println!("{}", t2 / t1);
}
