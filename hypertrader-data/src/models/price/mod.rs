use std::collections::HashMap;

use hyperliquid_rust_sdk::AllMids;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct PriceState {
    pub prices: HashMap<String, f64>,
}

impl From<AllMids> for PriceState {
    fn from(all_mids: AllMids) -> Self {
        let prices = all_mids
            .data
            .mids
            .into_iter()
            .filter(|(k, _)| k.starts_with("@"))
            .map(|(k, v)| (k, v.parse().unwrap_or_default()))
            .collect();
        Self { prices }
    }
}

#[derive(Debug, <PERSON>lone)]
pub struct Price {
    pub symbol: String,
    pub price: f64,
}

// serde from key value pair
impl Price {
    pub fn from_key_value(key: String, value: String) -> Self {
        Self {
            symbol: key,
            price: value.parse().unwrap_or_default(),
        }
    }
}
