use std::sync::Arc;

use hyperliquid_rust_sdk::{H160, UserStateResponse};
use hypertrader_hyperliquid::client::data::DataClient;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct UserState {
    pub address: H160,
    pub state: Arc<tokio::sync::RwLock<UserStateResponse>>,
}

impl UserState {
    pub async fn new(address: H160, data_client: &DataClient) -> anyhow::Result<Self> {
        let state = data_client.user_data(address).await?;
        let state = Arc::new(tokio::sync::RwLock::new(state));
        Ok(Self { address, state })
    }

    pub async fn update(&self, data_client: &DataClient) -> anyhow::Result<()> {
        let state = data_client.user_data(self.address).await?;
        *self.state.write().await = state;
        Ok(())
    }

    pub async fn get(&self) -> UserStateResponse {
        self.state.read().await.clone()
    }

    pub async fn update_with_value(&self, value: UserStateResponse) {
        *self.state.write().await = value;
    }
}
