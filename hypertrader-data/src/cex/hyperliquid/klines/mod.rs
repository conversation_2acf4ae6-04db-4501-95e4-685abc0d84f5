use dashmap::DashSet;
use hyperliquid_rust_sdk::{AssetMeta, CandleData, CandlesSnapshotResponse};
use hypertrader_clickhouse::{init_clickhouse_client, models::kline::Kline, ClickHouseClient};
use hypertrader_hyperliquid::client::data::DataClient;
use hypertrader_models::kline::interval::KlineInterval;
use kanal::{AsyncReceiver, AsyncSender};
use tokio::sync::Mutex;

const MIN_COIN_THRESHOLD: u32 = 0;
#[derive(Clone)]
pub struct DataKlineManager {
    pub subscribed_coins: DashSet<AssetMeta>,
    pub client: std::sync::Arc<DataClient>,

    pub clickhouse_client: std::sync::Arc<ClickHouseClient>,
    pub clickhouse_sender: AsyncSender<Vec<CandlesSnapshotResponse>>,
    pub clickhouse_receiver: std::sync::Arc<Mutex<AsyncReceiver<Vec<CandlesSnapshotResponse>>>>,

    // Add channels for WebSocket candle processing
    pub ws_candle_sender: AsyncSender<(String, String, CandleData)>,
    pub ws_candle_receiver: std::sync::Arc<Mutex<AsyncReceiver<(String, String, CandleData)>>>,
}

impl DataKlineManager {
    pub async fn new(client: std::sync::Arc<DataClient>) -> anyhow::Result<Self> {
        let clickhouse_client: ClickHouseClient = init_clickhouse_client().await?;
        let (clickhouse_sender, clickhouse_receiver) = kanal::unbounded_async();
        let (ws_candle_sender, ws_candle_receiver) = kanal::unbounded_async();

        Ok(Self {
            subscribed_coins: DashSet::new(),
            client,
            clickhouse_client: std::sync::Arc::new(clickhouse_client),
            clickhouse_sender,
            clickhouse_receiver: std::sync::Arc::new(Mutex::new(clickhouse_receiver)),
            // WebSocket candle processing channels
            ws_candle_sender,
            ws_candle_receiver: std::sync::Arc::new(Mutex::new(ws_candle_receiver)),
        })
    }

    pub async fn run(&self) -> anyhow::Result<()> {
        let clickhouse_receiver: std::sync::Arc<
            Mutex<AsyncReceiver<Vec<CandlesSnapshotResponse>>>,
        > = self.clickhouse_receiver.clone();
        let clickhouse_client = self.clickhouse_client.clone();
        tracing::info!("Starting to receive kline data");

        let _handler = tokio::spawn(async move {
            loop {
                let receiver = clickhouse_receiver.lock().await;
                while let Ok(klines) = receiver.recv().await {
                    if klines.is_empty() {
                        tracing::warn!("Received empty kline data");
                        continue;
                    }

                    let last_kline = klines.last().unwrap();
                    let last_kline_symbol = last_kline.coin.clone();
                    let last_kline_interval = last_kline.candle_interval.clone();
                    let last_kline_time = last_kline.time_close;
                    let last_kline_time_str =
                        chrono::DateTime::from_timestamp_millis(last_kline_time as i64)
                            .unwrap_or_default()
                            .to_rfc3339();

                    let klines_len = klines.len();

                    if let Err(e) = clickhouse_client.store_klines_history(klines, 1000).await {
                        tracing::error!("Failed to store klines: {}", e);
                    }

                    tracing::info!(
                        "Received and wrote {} {} klines for {}, last data time: {}",
                        klines_len,
                        last_kline_symbol,
                        last_kline_interval,
                        last_kline_time_str
                    );
                }
            }
        });

        Ok(())
    }

    // Save candles from WebSocket to ClickhouseDB
    pub async fn store_candle_from_ws(&self, coin: String, interval: String, candle: CandleData) -> anyhow::Result<()> {

        let kline = Kline {
            symbol: coin,
            interval: interval,
            time_open: candle.time_open as i64,
            time_close: candle.time_close as i64,
            open: candle.open.parse().unwrap_or_default(),
            high: candle.high.parse().unwrap_or_default(),
            low: candle.low.parse().unwrap_or_default(),
            close: candle.close.parse().unwrap_or_default(),
            volume: candle.volume.parse().unwrap_or_default(),
            num_trades: candle.num_trades,
        };

        self.clickhouse_client.insert_ohlc("ohlc_1m", kline).await
    }

    pub async fn store_candles_batch_from_ws(&self, candles: Vec<(String, String, CandleData)>) -> anyhow::Result<()> {
        if candles.is_empty() {
            return Ok(());
        }

        // Convert CandleData to Kline batch
        let mut kline_batch = hypertrader_clickhouse::models::kline::KlineBatch::new();

        for (coin, interval, candle) in candles {
            let kline = hypertrader_clickhouse::models::kline::Kline {
                symbol: coin,
                interval: interval,
                time_open: candle.time_open as i64,
                time_close: candle.time_close as i64,
                open: candle.open.parse().unwrap_or_default(),
                high: candle.high.parse().unwrap_or_default(),
                low: candle.low.parse().unwrap_or_default(),
                close: candle.close.parse().unwrap_or_default(),
                volume: candle.volume.parse().unwrap_or_default(),
                num_trades: candle.num_trades,
            };

            kline_batch.add(kline);
        }

        // Save batch to ClickhouseDB
        tracing::info!("Storing batch of {} candles to ClickHouseDB", kline_batch.len());
        self.clickhouse_client.insert_ohlc_batch("ohlc_1m", kline_batch).await
    }

    // some tasks call frequently
    pub async fn tick_job(&self) -> anyhow::Result<()> {
        let mut tasks = vec![];

        // optimize table to dedup data
        tasks.push(self.clickhouse_client.optimize_table());

        futures::future::try_join_all(tasks).await?;

        Ok(())
    }

    pub async fn fetch_subscribed_coins_klines(
        &self,
        interval: &KlineInterval,
        batch_size: usize,
    ) -> anyhow::Result<()> {
        // Get all subscribed coins
        let subscribed_coins = self.get_subscribed_coins();

        if subscribed_coins.is_empty() {
            tracing::info!("No subscribed coins, skipping kline data retrieval");
            return Ok(());
        }

        tracing::info!(
            "Starting batch retrieval of {} interval klines for {} coins, batch size: {}",
            subscribed_coins.len(),
            interval.to_string(),
            batch_size
        );

        // Split coin list into batches
        let total = subscribed_coins.len();

        let coins_to_fetch_map: dashmap::DashMap<String, AssetMeta> = dashmap::DashMap::new();
        for coin in subscribed_coins {
            coins_to_fetch_map.insert(coin.name.clone(), coin);
        }

        let mut retry_iters = 0;

        while coins_to_fetch_map.len() > 0 && retry_iters < 5 {
            retry_iters += 1;
            tracing::info!("Attempt {} to retrieve kline data", retry_iters);

            let mut coins_to_fetch_vec = coins_to_fetch_map
                .iter()
                .map(|asset_meta| asset_meta.clone())
                .collect::<Vec<_>>();

            if coins_to_fetch_map.len() % 2 == 0 {
                coins_to_fetch_vec.reverse();
            }

            // Process in batches
            for chunk in coins_to_fetch_vec.chunks(batch_size) {
                let mut tasks = Vec::with_capacity(batch_size);
                // Create tasks for current batch
                for coin in chunk {
                    let symbol = coin.name.as_str();
                    tracing::debug!("Creating task to fetch {} {} kline data", symbol, interval.to_string());
                    tasks.push(self.fetch_and_store_klines_all(
                        symbol,
                        interval,
                        &coins_to_fetch_map,
                    ));
                }

                // Execute current batch tasks concurrently
                futures::future::try_join_all(tasks).await?;
            }

            tracing::info!("Remaining coins to fetch kline data: {}", coins_to_fetch_map.len());

            // Wait to ensure network stability
            if coins_to_fetch_map.len() > 0 {
                // Incremental wait time
                tokio::time::sleep(std::time::Duration::from_secs(8 * retry_iters)).await;
            }
        }

        tracing::info!(
            "Completed retrieval of {} interval klines for all {} coins",
            total,
            interval.to_string()
        );

        Ok(())
    }

    pub async fn fetch_and_store_klines_all(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        to_fetch_map: &dashmap::DashMap<String, AssetMeta>,
    ) -> anyhow::Result<()> {
        let klines = self.fetch_kline_history_all(symbol, interval).await;
        let klines_len = klines.len();

        self.clickhouse_sender.send(klines).await?;

        if klines_len > 0 {
            to_fetch_map.remove(symbol);
            tracing::info!(
                "{} {} klines stored: {} records",
                klines_len,
                symbol,
                interval.to_string()
            );
        } else {
            tracing::info!(
                "{} only received {} {} klines",
                symbol,
                klines_len,
                interval.to_string()
            );
        }

        Ok(())
    }

    pub async fn store_klines(&self, klines: Vec<CandlesSnapshotResponse>) -> anyhow::Result<()> {
        self.clickhouse_sender.send(klines).await?;
        Ok(())
    }

    pub async fn fetch_kline_history_all(
        &self,
        symbol: &str,
        interval: &KlineInterval,
    ) -> Vec<CandlesSnapshotResponse> {
        self.client
            .klines_history(symbol.to_string(), 0, 2690569402225, interval)
            .await
            .unwrap_or_default()
    }

    pub async fn fetch_kline_history(
        &self,
        symbol: &str,
        start_time: u64,
        end_time: u64,
        interval: KlineInterval,
    ) -> Vec<CandlesSnapshotResponse> {
        self.client
            .klines_history(symbol.to_string(), start_time, end_time, &interval)
            .await
            .unwrap_or_default()
    }

    pub async fn init(&self) -> anyhow::Result<()> {
        // init default subscribed coins
        self.add_default_subscribed_coins_from_meta(MIN_COIN_THRESHOLD)
            .await;

        // create kline table if not exists
        self.clickhouse_client.create_kline_table().await?;

        let intervals = [
            ("1m", "1", "MINUTE"),
            ("3m", "3", "MINUTE"),
            ("5m", "5", "MINUTE"),
            ("15m", "15", "MINUTE"),
            ("30m", "30", "MINUTE"),
            ("1h", "1", "HOUR"),
            ("2h", "2", "HOUR"),
            ("4h", "4", "HOUR"),
            ("8h", "8", "HOUR"),
            ("12h", "12", "HOUR"),
            ("1d", "1", "DAY"),
            ("3d", "3", "DAY"),
            ("1w", "1", "WEEK"),
            ("1M", "1", "MONTH"),
        ];

        // Create OHLC tables and views for all intervals
        for (interval_name, interval_duration, interval_unit) in intervals {
            self.clickhouse_client
                .create_ohlc_table_and_view(interval_name, interval_duration, interval_unit)
                .await?;
        }


        // get all kline ranges
        let kline_ranges = self.clickhouse_client.get_all_kline_ranges().await?;

        if kline_ranges.is_empty() {
            tracing::info!("No kline data found");
            return Ok(());
        }

        // Henry: Comment out the following lines to test the code logic of Simply sending the candle to the channel for batch processing, this not impacting the code logic
        // // Start the WebSocket candle processor
        // self.run_ws_candle_processor().await?;

        // Henry: Comment out the following line to avoid printing all kline ranges too much, not impacting the code logic
        // for kline_range in kline_ranges {
        //     tracing::info!("kline_range: {}", kline_range.to_string_with_verified());
        // }

        Ok(())
    }

    /// get subscribed coins
    pub fn get_subscribed_coins(&self) -> Vec<AssetMeta> {
        self.subscribed_coins
            .iter()
            .map(|asset_meta| asset_meta.clone())
            .collect()
    }

    pub fn get_subscribed_coins_limit(&self, limit: usize) -> Vec<AssetMeta> {
        self.subscribed_coins
            .iter()
            .take(limit)
            .map(|asset_meta| asset_meta.clone())
            .collect()
    }

    /// get subscribed coin names
    pub fn get_subscribed_coin_names(&self) -> Vec<String> {
        self.subscribed_coins
            .iter()
            .map(|asset_meta| asset_meta.name.clone())
            .collect()
    }

    pub fn add_sub(&self, asset_meta: AssetMeta) {
        self.subscribed_coins.insert(asset_meta);
    }

    pub async fn add_default_subscribed_coins_from_meta(&self, leverage_threshold: u32) {
        // default assets are assets
        for asset in self.client.meta.read().await.universe.iter() {
            if asset.max_leverage >= leverage_threshold {
                self.subscribed_coins.insert(asset.clone());
            }
        }

        tracing::info!("Added {} default subscribed assets", self.subscribed_coins.len());
    }

    pub fn set_subscribed_coins(&self, subscribed_coins: Vec<AssetMeta>) {
        self.subscribed_coins.clear();
        for coin in subscribed_coins {
            self.subscribed_coins.insert(coin);
        }
    }

    pub fn remove_sub(&self, asset_meta: AssetMeta) {
        self.subscribed_coins.remove(&asset_meta);
    }

    pub fn remove_sub_by_symbol(&self, symbol: &str) {
        self.subscribed_coins
            .retain(|asset_meta| asset_meta.name != symbol);
    }
}
