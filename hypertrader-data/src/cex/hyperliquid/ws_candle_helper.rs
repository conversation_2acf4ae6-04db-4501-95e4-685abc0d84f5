use std::sync::Arc;
use dashmap::DashMap;
use hyperliquid_rust_sdk::CandleData;

use super::klines;

/// Check if a new minute needs to be processed
pub fn should_process_new_minute(
    current_minute: u64,
    new_minute: u64,
    processed_minutes: &std::collections::HashSet<u64>
) -> bool {
    current_minute != 0 &&
    new_minute > current_minute &&
    !processed_minutes.contains(&current_minute)
}

/// Process when switching to a new minute
pub async fn process_minute_change(
    previous_minute: u64,
    new_minute: u64,
    processed_minutes: &mut std::collections::HashSet<u64>,
    best_candles: &Arc<DashMap<(String, u64, String), CandleData>>,
    kline_manager: &klines::DataKlineManager,
) {
    // Mark the minute as processed
    processed_minutes.insert(previous_minute);

    tracing::info!(
        "Minute changed from {} to {}, processing stored candles",
        previous_minute, new_minute
    );

    // Process candles from the previous minute
    process_candles_for_minute(previous_minute, best_candles, kline_manager).await;

    // Clean up the list of processed minutes to avoid unlimited growth
    cleanup_processed_minutes(processed_minutes);

    // Optimize all tables from 3m to 1M
    let _ = kline_manager.clickhouse_client.optimize_all_tables().await;
}

/// Process and save the best candles for a specific minute
pub async fn process_candles_for_minute(
    minute: u64,
    best_candles: &Arc<DashMap<(String, u64, String), CandleData>>,
    kline_manager: &klines::DataKlineManager,
) {
    // Get the list of keys to process
    let keys_to_process: Vec<(String, u64, String)> = best_candles
        .iter()
        .map(|entry| entry.key().clone())
        .filter(|key| key.1 == minute)
        .collect();

    if keys_to_process.is_empty() {
        return;
    }

    // Create a batch for processing
    let mut candles_batch = Vec::with_capacity(keys_to_process.len());
    for key in keys_to_process {
        if let Some((_, best_candle)) = best_candles.remove(&key) {
            candles_batch.push((
                best_candle.coin.clone(),
                best_candle.interval.clone(),
                best_candle
            ));
        }
    }

    // Save the batch to the database
    if !candles_batch.is_empty() {
        match kline_manager.store_candles_batch_from_ws(candles_batch.clone()).await {
            Ok(_) => {
                tracing::info!(
                    "Successfully stored batch of {} candles for minute {}",
                    candles_batch.len(), minute
                );
            },
            Err(err) => {
                tracing::error!(
                    "Failed to store batch of {} candles: {}",
                    candles_batch.len(), err
                );
            }
        }
    }
}

/// Clean up the list of processed minutes
pub fn cleanup_processed_minutes(processed_minutes: &mut std::collections::HashSet<u64>) {
    if processed_minutes.len() > 60 { // Keep about 1 hour
        let minutes_to_keep: Vec<_> = processed_minutes
            .iter()
            .copied()
            .collect::<Vec<_>>();

        if minutes_to_keep.len() > 30 {
            processed_minutes.clear();
            // Keep the most recent 30 minutes
            for &min in minutes_to_keep.iter().rev().take(30) {
                processed_minutes.insert(min);
            }
        }
    }
}

/// Update the map storing the best candles
pub fn update_best_candle(
    coin: String,
    minute_timestamp: u64,
    interval: String,
    candle_data: CandleData,
    best_candles: &Arc<DashMap<(String, u64, String), CandleData>>,
) {
    let key = (coin, minute_timestamp, interval);

    match best_candles.entry(key) {
        dashmap::mapref::entry::Entry::Occupied(mut entry) => {
            let existing_candle = entry.get();
            // Only update if the new candle has a higher num_trades
            if candle_data.num_trades > existing_candle.num_trades {
                entry.insert(candle_data);
            }
        },
        dashmap::mapref::entry::Entry::Vacant(entry) => {
            entry.insert(candle_data);
        }
    }
}
