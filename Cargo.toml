[package]
name = "hypertrader"
version = "0.1.0"
edition = "2024"

[workspace]
members = [
    ".",
    "hypertrader-utils",
    "hypertrader-utils/hypertrader-dbs",
    "hypertrader-utils/hypertrader-cache",
    "hypertrader-utils/hypertrader-clickhouse",
    "hypertrader-hyperliquid",
    "hypertrader-core",
    "hypertrader-data",
    "hypertrader-examples",
    "hypertrader-sdk",
    "hypertrader-models",
    "hypertrader-services",
]

[workspace.dependencies]
# cache and update data from hyperliquid and other sources.
hypertrader-data = { path = "./hypertrader-data" }
hypertrader-models = { path = "./hypertrader-models" }
# process data from hyperliquid and order commands
hypertrader-core = { path = "./hypertrader-core" }
hypertrader-utils = { path = "./hypertrader-utils" }
hypertrader-dbs = { path = "./hypertrader-utils/hypertrader-dbs" }
hypertrader-cache = { path = "./hypertrader-utils/hypertrader-cache" }
hypertrader-clickhouse = { path = "./hypertrader-utils/hypertrader-clickhouse" }

hypertrader-hyperliquid = { path = "./hypertrader-hyperliquid" }

# hyperliquid_rust_sdk = "0.5.0"
# 请使用自己维护的 hyperliquid-rust-sdk
# TODO: Username and password should be set in env or secret password management later, this access token is only has read access so it is safe.
hyperliquid_rust_sdk = { git = "https://gitlab+deploy-token-5:<EMAIL>/xbit/xbit-dex/xbit-hyperliquid-sdk.git", branch = "unstable" }

# async
tokio = { version = "1.44.1" }
futures = "0.3.31"
dashmap = "6.1.0"


# error handling
anyhow = "1.0.97"
thiserror = "2.0.12"

# serde
serde = { version = "1.0.219" }
serde_json = "1.0"

# server
salvo = { version = "0.77.0" }

# log
log = "0.4.27"
tracing-subscriber = "0.3.19"
tracing = "0.1.41"

# static
lazy_static = "1.5.0"

# env
dotenvy = "0.15.7"

# db
sea-orm = "1.1.7"

# http
reqwest = { version = "0.11" }

chrono = { version = "0.4.40", features = ["serde"] }
clickhouse = { version = "0.13.2", features = ["rustls-tls"] }


[dependencies]
hyperliquid_rust_sdk = { workspace = true }
hypertrader-utils = { workspace = true }
hypertrader-dbs = { workspace = true }
hypertrader-data = { workspace = true }
clickhouse = { workspace = true }

anyhow = { workspace = true }                       # error handling
thiserror = { workspace = true }                    # error handling
serde_json = { workspace = true }                   # json parser
tracing = { workspace = true }                      # logging
tracing-subscriber = { workspace = true }           # logging
lazy_static = { workspace = true }                  # static variables
dotenvy = { workspace = true }                      # env
reqwest = { workspace = true, features = ["json"] } # http client
tokio = { workspace = true, features = ["full"] }   # async runtime

dashmap = { workspace = true } # thread safe hashmap, multi process, high performance
salvo = { workspace = true, features = [ # async http server
    "rustls",
    "anyhow",
    "cors",
    "affix-state",
    "jwt-auth",
    "websocket",
] }

sea-orm = "1.1.7"
#hyperliquid_rust_sdk = { path = "../xbit-hyperliquid-sdk" }
sea-orm-migration = "^1.1"

futures-util = "0.3.31"
tokio-stream = { version = "0.1.17", features = ["net"] }
serde = { version = "1.0.219", features = ["derive"] }
uuid = { version = "1.16.0", features = ["v4"] }
chrono = { workspace = true, features = ["serde"] }
static_init = "1.0.3"
hex = "0.4.3"
stdext = "0.3.3"