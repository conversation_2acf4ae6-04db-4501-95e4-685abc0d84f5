<?xml version="1.0" encoding="UTF-8"?>
<module type="EMPTY_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-core/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-data/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-examples/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-hyperliquid/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-models/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-sdk/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-utils/hypertrader-cache/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-utils/hypertrader-clickhouse/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-utils/hypertrader-dbs/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-utils/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hypertrader-services/src" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>