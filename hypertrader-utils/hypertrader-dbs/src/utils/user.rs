use super::*;

use crate::schemas::user;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct UserManager {
    pub db: DatabaseConnection,
}

impl UserManager {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }

    pub async fn create_user(&self, name: String, email: String) -> Result<user::Model, DbErr> {
        let user = user::ActiveModel {
            username: ActiveValue::Set(name),
            email: ActiveValue::Set(email),
            ..Default::default()
        };
        let user = user.insert(&self.db).await?;
        Ok(user)
    }

    pub async fn read_user_by_id(&self, id: i32) -> Result<user::Model, DbErr> {
        let user = user::Entity::find_by_id(id).one(&self.db).await?;

        if let Some(user) = user {
            Ok(user)
        } else {
            Err(DbErr::RecordNotFound("User not found".to_string()))
        }
    }

    pub async fn update_user(
        &self,
        id: i32,
        name: String,
        email: String,
    ) -> Result<user::Model, DbErr> {
        let user = user::Entity::find_by_id(id).one(&self.db).await?;

        if let Some(user) = user {
            let mut user: user::ActiveModel = user.into();
            user.username = ActiveValue::Set(name);
            user.email = ActiveValue::Set(email);
            let user = user.update(&self.db).await?;
            Ok(user)
        } else {
            Err(DbErr::RecordNotFound("User not found".to_string()))
        }
    }

    pub async fn delete_user(&self, id: i32) -> Result<(), DbErr> {
        let user = user::Entity::find_by_id(id).one(&self.db).await?;
        if let Some(user) = user {
            user.delete(&self.db).await?;
            Ok(())
        } else {
            Err(DbErr::RecordNotFound("User not found".to_string()))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_create_user() -> anyhow::Result<()> {
        let db = init_db().await?;
        let user_manager = UserManager::new(db);
        let user = user_manager
            .create_user("test111vvv".to_string(), "<EMAIL>".to_string())
            .await?;
        println!("user: {:?}", user);
        Ok(())
    }
}
