use std::collections::HashMap;

use sea_orm::{ActiveValue, DatabaseConnection, EntityTrait};

use super::schemas_postgres::prices::{self, ActiveModel};

pub struct Price {
    pub token_symbol: String,
    pub price: f64,
}

impl From<(String, String)> for Price {
    fn from(value: (String, String)) -> Self {
        Self {
            token_symbol: value.0,
            price: value.1.parse::<f64>().unwrap_or_default(),
        }
    }
}

#[derive(Clone, Debug)]
pub struct PricesManager {
    pub db: DatabaseConnection,
}

impl PricesManager {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

impl PricesManager {
    pub async fn insert_prices_map(
        &self,
        prices_map: HashMap<String, String>,
    ) -> anyhow::Result<()> {
        let timestamp = chrono::Utc::now().timestamp_millis();

        let mut price_models = Vec::new();
        prices_map.into_iter().for_each(|(token_symbol, price)| {
            let price = Price::from((token_symbol, price));
            let price_model = ActiveModel {
                token_symbol: ActiveValue::Set(price.token_symbol),
                price: ActiveValue::Set(price.price),
                timestamp: ActiveValue::Set(timestamp),
            };
            price_models.push(price_model);
        });

        let price_models = prices::Entity::insert_many(price_models)
            .exec(&self.db)
            .await?;

        tracing::info!("Inserted prices: {:?}", price_models);

        Ok(())
    }
}
