use schemas::vault;
use sea_orm::{<PERSON>umn<PERSON><PERSON><PERSON>, DeleteR<PERSON>ult, QueryFilter};
pub mod models;
use super::*;
use sea_orm::ActiveModelTrait;

#[derive(Debug, Clone)]
pub struct VaultManager {
    pub db: DatabaseConnection,
}

impl VaultManager {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }

    pub async fn create_vault(
        &self,
        vault: models::create::CreateVault,
    ) -> anyhow::Result<vault::Model> {
        let vault = vault::ActiveModel {
            vault_name: ActiveValue::Set(vault.vault_name),
            vault_address: ActiveValue::Set(vault.vault_address),
            vault_owner_address: ActiveValue::Set(vault.vault_owner_address),
            is_signal: ActiveValue::Set(vault.is_signal as i8),
            ..Default::default()
        };

        let res = vault.insert(&self.db).await?;
        Ok(res)
    }

    pub async fn list_vaults(&self) -> anyhow::Result<Vec<vault::Model>> {
        let vaults = vault::Entity::find()
            .filter(vault::Column::DeletedAt.is_null())
            .all(&self.db)
            .await?;
        Ok(vaults)
    }

    pub async fn delete_vault(&self, vault_address: String) -> anyhow::Result<DeleteResult> {
        let res: DeleteResult = vault::Entity::delete_by_id(vault_address)
            .exec(&self.db)
            .await?;
        Ok(res)
    }
}

#[cfg(test)]
mod tests {

    use super::*;

    #[tokio::test]
    async fn test_delete_vault() -> anyhow::Result<()> {
        let db = init_db().await?;
        let vault_manager = VaultManager::new(db);
        let res = vault_manager
            .delete_vault("0x1234567890123451312321321".to_string())
            .await?;
        println!("res: {:?}", res);
        Ok(())
    }

    #[tokio::test]
    async fn test_create_vault2() -> anyhow::Result<()> {
        let db = init_db().await?;
        let vault_manager = VaultManager::new(db);

        let vault = models::create::CreateVault {
            vault_name: "test1313".to_string(),
            vault_address: "0x1234567890123451312321321".to_string(),
            vault_owner_address: "0x12345678901231313456789012345678901234567890".to_string(),
            is_signal: true,
        };

        let vault = vault_manager.create_vault(vault).await?;
        println!("vault: {:?}", vault);

        Ok(())
    }

    #[tokio::test]
    async fn test_list_vaults() -> anyhow::Result<()> {
        let db = init_db().await?;
        let vault_manager = VaultManager::new(db);
        let vaults = vault_manager.list_vaults().await?;
        println!("vaults: {:#?}", vaults);
        Ok(())
    }
}
