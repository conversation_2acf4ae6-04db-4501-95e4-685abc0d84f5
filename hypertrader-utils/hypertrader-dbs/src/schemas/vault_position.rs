//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.7

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, serde::Serialize, serde::Deserialize)]
#[sea_orm(table_name = "vault_position")]
pub struct Model {
    #[sea_orm(column_name = "position_id", primary_key)]
    pub position_id: i64,
    #[sea_orm(column_name = "user_id")]
    pub user_id: i32,
    #[sea_orm(column_name = "mode")]
    pub mode: String, // cross or isolate

    #[sea_orm(column_name = "symbol")]
    pub symbol: String,
    #[sea_orm(column_name = "amount")]
    pub size: f64,
    #[sea_orm(column_name = "is_open")]
    pub is_open: bool,
    // #[sea_orm(column_name = "LEADER")]
    // pub leader: Option<String>,
    #[sea_orm(column_name = "entry_price")]
    pub entry_price: f64,
    // #[sea_orm(column_name = "current_price")]
    // pub current_price: f64,
    // #[sea_orm(column_name = "collateral_used")]
    // pub collateral_use: f64,
    #[sea_orm(column_name = "tp_price")]
    pub tp_price: Option<f64>,
    #[sea_orm(column_name = "sp_price")]
    pub sp_price: Option<f64>,
    #[sea_orm(column_name = "direction")]
    pub direction: String,
    #[sea_orm(column_name = "leverage")]
    pub leverage: Option<i32>,
    // #[sea_orm(column_name = "sort")]
    // pub sort: Option<i32>,
    // pub created_date: Option<DateTimeUtc>,
    pub updated_date: Option<DateTimeUtc>,

    // #[sea_orm(column_name = "IS_DISABLED")]
    // pub is_disabled: Option<i8>,
    // #[sea_orm(column_name = "UPDATED_DATE")]

    // #[sea_orm(column_name = "CREATED_BY")]
    // pub created_by: Option<String>,
    // #[sea_orm(column_name = "UPDATED_DATE")]
    // pub updated_date: Option<DateTime>,
    // #[sea_orm(column_name = "UPDATED_BY")]
    // pub updated_by: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
