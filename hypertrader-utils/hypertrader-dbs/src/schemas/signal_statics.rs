//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.7

use sea_orm::entity::prelude::*;
#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel,serde::Serialize, serde::Deserialize)]
#[sea_orm(table_name = "signal_statics")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub date: Date,
    pub position_value: f64,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
