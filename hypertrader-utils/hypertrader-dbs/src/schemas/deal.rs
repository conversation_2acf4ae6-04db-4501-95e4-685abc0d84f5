//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.7

use sea_orm::{ActiveValue, Database};
use sea_orm::entity::prelude::*;
use sea_orm::sqlx::types::chrono::Utc;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, serde::Serialize, serde::Deserialize)]
#[sea_orm(table_name = "trade_deals")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub deal_id: i32,
    // pub uuid: String,
    #[sea_orm(created_at, default_current_timestamp)]
    pub user_id: i32,
    pub time: DateTimeUtc,
    pub coin: String,
    pub dir:  String,
    pub px:   f64,
    pub sz:   f64,
    pub fee:  f64,
    pub closed_pnl: f64,
    pub closed_pnl_rate: f64,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}
impl ActiveModelBehavior for ActiveModel {

}
