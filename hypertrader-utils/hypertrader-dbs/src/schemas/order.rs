//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.7

use sea_orm::{ActiveValue, Database};
use sea_orm::entity::prelude::*;
use sea_orm::sqlx::types::chrono::Utc;

// #[derive(Clone, Debug, serde::Serialize, serde::Deserialize)]
// #[sea_orm(table_name = "trade_orders")]

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, serde::Serialize, serde::Deserialize)]
#[sea_orm(table_name = "trade_orders")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub order_id: i32,
    pub user_id: i32,
    pub symbol: String,
    pub order_type: String,
    pub mode: String, 
    pub order_action_type: String,
    pub is_active: bool, // 修改为 Option 类型
    pub limit_price: f64,
    // #[sea_orm(column_name = "SIZE", column_type = "f64", nullable)]
    pub size: f64,
    // pub uuid: String,
    pub created_date: Option<DateTimeUtc>,
    // pub updated_date: Option<DateTimeUtc>,
    #[sea_orm(column_name = "tp_price")]
    pub tp_price: Option<f64>,
    #[sea_orm(column_name = "sp_price")]
    pub sp_price: Option<f64>,

    #[sea_orm(column_name = "leverage")]
    pub leverage: Option<i32>,

    pub signal_id: Option<i32>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}
impl ActiveModelBehavior for ActiveModel {

}
