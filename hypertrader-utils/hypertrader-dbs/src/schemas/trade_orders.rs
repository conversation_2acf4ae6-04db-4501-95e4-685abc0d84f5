//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.7

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq)]
#[sea_orm(table_name = "trade_orders")]
pub struct Model {
    #[sea_orm(column_name = "ORDER_ID", primary_key)]
    pub order_id: i64,
    #[sea_orm(column_name = "PAIR_ID")]
    pub pair_id: Option<i32>,
    #[sea_orm(column_name = "VAULT_ID")]
    pub vault_id: i32,
    #[sea_orm(column_name = "ORDER_TYPE")]
    pub order_type: String,
    #[sea_orm(column_name = "ORDER_ACTION_TYPE")]
    pub order_action_type: String,
    #[sea_orm(column_name = "ORDER_STATUS")]
    pub order_status: String,
    #[sea_orm(column_name = "IS_REDUCE_ONLY")]
    pub is_reduce_only: Option<String>,
    #[sea_orm(column_name = "IS_ACTIVE")]
    pub is_active: i8,
    #[sea_orm(column_name = "PRICE", column_type = "Decimal(Some((20, 8)))")]
    pub price: Decimal,
    #[sea_orm(column_name = "SIZE", column_type = "Decimal(Some((20, 8)))", nullable)]
    pub size: Option<Decimal>,
    #[sea_orm(
        column_name = "FILLED_PRICE",
        column_type = "Decimal(Some((20, 8)))",
        nullable
    )]
    pub filled_price: Option<Decimal>,
    #[sea_orm(
        column_name = "COMMISSION",
        column_type = "Decimal(Some((20, 8)))",
        nullable
    )]
    pub commission: Option<Decimal>,
    #[sea_orm(column_name = "VERSION")]
    pub version: i32,
    #[sea_orm(column_name = "ACCOUNT_ID")]
    pub account_id: i64,
    #[sea_orm(column_name = "USER_ID")]
    pub user_id: Option<i64>,
    #[sea_orm(column_name = "FUND_TX_ID")]
    pub fund_tx_id: Option<i64>,
    #[sea_orm(column_name = "TP_PRICE", column_type = "Decimal(Some((20, 8)))")]
    pub tp_price: Decimal,
    #[sea_orm(column_name = "SP_PRICE", column_type = "Decimal(Some((20, 8)))")]
    pub sp_price: Decimal,
    #[sea_orm(column_name = "CREATED_BY")]
    pub created_by: Option<String>,
    #[sea_orm(column_name = "CREATED_DATE")]
    pub created_date: Option<DateTime>,
    #[sea_orm(column_name = "UPDATED_BY")]
    pub updated_by: Option<String>,
    #[sea_orm(column_name = "UPDATED_DATE")]
    pub updated_date: DateTimeUtc,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
