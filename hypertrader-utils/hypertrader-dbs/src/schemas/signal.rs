//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.7

use sea_orm::entity::prelude::*;

// #[derive(Clone, Debug, PartialEq, DeriveEntityModel,serde::Serialize, serde::Deserialize)]
// #[sea_orm(table_name = "symbol_signal")]
// pub struct Model {
//     #[sea_orm(primary_key)]
//     pub order_id: i32,
//     pub pair_id: String,
//     pub vault_id: i32,
//     pub order_type: String,
//     pub leverage: i32,
//     pub is_reduce_only: Option<String>, // 修改为 Option 类型
//     pub is_active: Option<String>, // 修改为 Option 类型
//     pub price: Decimal,
//     #[sea_orm(column_name = "SIZE", column_type = "Decimal(Some((20, 8)))", nullable)]
//     pub size: Option<Decimal>,
//     pub created_date: Option<DateTimeUtc>,
//     pub updated_date: Option<DateTimeUtc>,
// }


#[derive(Clone, Debug, PartialEq, DeriveEntityModel,serde::Serialize, serde::Deserialize)]
#[sea_orm(table_name = "symbol_signal")]
pub struct Model {
    #[sea_orm(primary_key)]
    // pub sig_id: i32,
    // pub order_id: i32,
    pub id: i32,
    pub username: String,
    pub uuid: String,
    pub signal_type: String, // candidate/best
    pub signal_name: String,
    pub signal_pnl:  f64,
    pub running_status: String, // running/reach_tp/reach_sp/closed
    pub consecutive_wins: i32,
    pub subscribers: i32,
    pub operation_direction: String, // open_long / open_short / close
    pub underlying_asset: String,
    pub monthly_return_rate: f64,
    pub monthly_alpha: f64,
    pub annual_win_rate: f64,
    pub cumulative_income: f64,

    pub sharpe_ratio: f64,
    pub three_yield: f64,
    pub seven_yield: f64,
    pub max_drawdown_7days: f64,
    pub running_time: f64,
    pub historical_win_rate: f64,
    pub profit_loss_count: f64,
    pub profit_loss_ratio: f64,
    pub confidence: String,  // high, middle, low,
    pub evaluation_status: String, // qualified, unqualified, qualifying
    pub review: String,

    pub user_id: i32,

    #[sea_orm(created_at, default_current_timestamp)]
    pub created_date: Option<DateTimeUtc>,

    #[sea_orm(updated_at, default_current_timestamp)]
    pub updated_date: Option<DateTimeUtc>,
}



#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
