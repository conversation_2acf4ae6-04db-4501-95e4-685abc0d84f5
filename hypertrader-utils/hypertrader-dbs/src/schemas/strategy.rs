//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.7

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel,serde::Serialize, serde::Deserialize)]
#[sea_orm(table_name = "strategy")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub strategy_id: i32,
    pub strategy_owner: String,
    pub strategy_uuid: Option<String>,
    pub strategy_name: String,
    pub is_prefered_strategy: i32,
    pub vault_address: Option<String>, // 修改为 Option 类型
    pub created_date: Option<DateTimeUtc>,
    pub updated_date: Option<DateTimeUtc>,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
