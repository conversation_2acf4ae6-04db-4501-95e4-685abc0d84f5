use sea_orm::ConnectOptions;
use hypertrader_utils::env::GLOBAL_ENVS;

use super::*;

pub async fn init_db() -> Result<DatabaseConnection, DbErr> {
    // Ensure environment variables are initialized
    // This would typically be done at application startup, but added here as a safeguard
    if GLOBAL_ENVS.get_environment() == "uninitialized" {
        GLOBAL_ENVS.init();
    }

    // Use the postgres_url method to get connection string from environment variables
    let postgres_url = GLOBAL_ENVS.postgres_url();
    let mut opt = ConnectOptions::new(postgres_url);

    opt.sqlx_logging(false)
        .sqlx_logging_level(log::LevelFilter::Trace);

    let db = Database::connect(opt).await?;

    Ok(db)
}