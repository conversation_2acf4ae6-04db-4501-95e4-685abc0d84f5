[package]
name = "hypertrader-dbs"
version = "0.1.0"
edition = "2024"

[dependencies]
hypertrader-utils = { workspace = true }
sea-orm = { workspace = true, features = [
    "runtime-tokio-rustls",
    "debug-print",
    "sqlx-postgres",
    "with-chrono",
    # "sqlx-mysql",
    # "with-rust_decimal",
    # "with-bigdecimal",
    # "with-time",
] }
tokio = { workspace = true, features = ["full"] }
serde = { workspace = true, features = ["derive"] }
anyhow = { workspace = true }
chrono = { workspace = true }
tracing = { workspace = true }
log = { workspace = true }
