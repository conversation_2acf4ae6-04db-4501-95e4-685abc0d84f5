[package]
name = "hypertrader-utils"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow.workspace = true
thiserror.workspace = true
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

salvo = { workspace = true, features = [
    "rustls",
    "jwt-auth",
    "cors",
    "anyhow",
    "session",
] }

dotenvy = { workspace = true }
lazy_static = { workspace = true }

static_init = "1.0.3"
sea-orm = "1.1.7"
log = "0.4.27"


[build-dependencies]
serde_json = { workspace = true }
