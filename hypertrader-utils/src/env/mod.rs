use dotenvy::dotenv;
use std::env;
use std::sync::OnceLock;
use tracing::{debug, error, info};
use std::path::Path;

// Global static environment variables instance
lazy_static::lazy_static! {
    pub static ref GLOBAL_ENVS: GlobalEnvs = GlobalEnvs::new();
}

// Internal environment structure to store actual data
#[derive(Debug, <PERSON>lone)]
pub struct GlobalEnvsInner {
    pub hyper_secret: String,
    pub hyper_vault: String,

    // Clickhouse database connection details
    pub clickhouse_host: String,
    pub clickhouse_port: String,
    pub clickhouse_key_id: Option<String>,
    pub clickhouse_key_secret: Option<String>,
    pub clickhouse_db: Option<String>,

    // Postgres database connection details
    pub postgres_host: String,
    pub postgres_port: String,
    pub postgres_user: String,
    pub postgres_pass: String,
    pub postgres_db: String,
}

impl GlobalEnvsInner {
    // Initialize from environment variables
    fn new() -> Self {
        let hyper_secret = env::var("HYPER_SECRET").unwrap_or_else(|_| {
            error!("HYPER_SECRET environment variable not set, using default value");
            "default_secret_value".to_string()
        });

        let hyper_vault = env::var("HYPER_VAULT").unwrap_or_else(|_| {
            error!("HYPER_VAULT environment variable not set, using default value");
            "default_vault_value".to_string()
        });

        let clickhouse_host = env::var("CLICKHOUSE_HOST").unwrap_or_else(|_| {
            error!("CLICKHOUSE_HOST not set");
            "localhost".to_string()
        });

        let clickhouse_port = env::var("CLICKHOUSE_PORT").unwrap_or_else(|_| {
            error!("CLICKHOUSE_PORT not set");
            "8123".to_string()
        });

        let clickhouse_key_id = env::var("CLICKHOUSE_USER").ok();
        let clickhouse_key_secret = env::var("CLICKHOUSE_PASS").ok();
        let clickhouse_db = env::var("CLICKHOUSE_DB").ok();

        let postgres_host = env::var("POSTGRES_HOST").unwrap_or_else(|_| {
            error!("POSTGRES_HOST not set");
            "localhost".to_string()
        });

        let postgres_port = env::var("POSTGRES_PORT").unwrap_or_else(|_| {
            error!("POSTGRES_PORT not set");
            "5432".to_string()
        });

        let postgres_user = env::var("POSTGRES_USER").unwrap_or_else(|_| {
            error!("POSTGRES_USER not set");
            "postgres".to_string()
        });

        let postgres_pass = env::var("POSTGRES_PASS").unwrap_or_else(|_| {
            error!("POSTGRES_PASS not set");
            "123456".to_string()
        });

        let postgres_db = env::var("POSTGRES_DB").unwrap_or_else(|_| {
            error!("POSTGRES_DB not set");
            "dex".to_string()
        });

        Self {
            hyper_secret,
            hyper_vault,
            // Clickhouse connection details
            clickhouse_host,
            clickhouse_port,
            clickhouse_key_id,
            clickhouse_key_secret,
            clickhouse_db,
            // Postgres connection details
            postgres_host,
            postgres_port,
            postgres_user,
            postgres_pass,
            postgres_db,
        }
    }
}

// Global environment structure encapsulating OnceLock<GlobalEnvsInner>
pub struct GlobalEnvs(OnceLock<GlobalEnvsInner>);

impl GlobalEnvs {
    pub fn new() -> Self {
        Self(OnceLock::new())
    }

    pub fn clickhouse_url(&self) -> String {
        let clickhouse_host = &self.get_inner().clickhouse_host;
        let clickhouse_port = &self.get_inner().clickhouse_port;
        format!("http://{}:{}", clickhouse_host, clickhouse_port)
    }

    pub fn clickhouse_key_id(&self) -> Option<&str> {
        self.get_inner().clickhouse_key_id.as_deref()
    }

    pub fn clickhouse_key_secret(&self) -> Option<&str> {
        self.get_inner().clickhouse_key_secret.as_deref()
    }

    pub fn clickhouse_db(&self) -> Option<&str> {
        self.get_inner().clickhouse_db.as_deref()
    }

    pub fn postgres_url(&self) -> String {
        let inner = self.get_inner();
        format!(
            "postgres://{}:{}@{}:{}/{}",
            inner.postgres_user,
            inner.postgres_pass,
            inner.postgres_host,
            inner.postgres_port,
            inner.postgres_db
        )
    }

    // Get internal instance (for internal use)
    fn get_inner(&self) -> &GlobalEnvsInner {
        self.0
            .get()
            .expect("Environment variables not initialized, please call GLOBAL_ENVS.init() first")
    }

    // Initialize environment variables
    pub fn init(&self) {
        // Get current environment from STAGE env var
        let stage = self.get_environment();
        info!("Initializing with environment: {}", stage);

        // Primary path: env/{stage}.env (similar to Go implementation)
        let env_file_path = format!("env/{}.env", stage);
        let mut loaded = false;

        // Try loading from the primary path
        if Path::new(&env_file_path).exists() {
            match dotenvy::from_filename(&env_file_path) {
                Ok(path) => {
                    info!("Loaded environment variables from {}", path.display());
                    loaded = true;
                }
                Err(e) => {
                    error!("Failed to load environment from {}: {}", env_file_path, e);
                }
            }
        } else {
            info!("Environment file {} not found", env_file_path);
        }

        // Fallback paths for backward compatibility
        let fallback_paths = [
            format!("../../../env/.env.{}", stage),
            format!(".env.{}", stage),
            "env/.env".to_string(),
        ];

        for path in fallback_paths {
            if loaded {
                break;
            }

            if Path::new(&path).exists() {
                match dotenvy::from_filename(&path) {
                    Ok(file_path) => {
                        info!("Loaded environment variables from {}", file_path.display());
                        loaded = true;
                    }
                    Err(e) => {
                        debug!("Failed to load environment from {}: {}", path, e);
                    }
                }
            }
        }

        // Last resort: try the default .env file
        if !loaded {
            match dotenv() {
                Ok(path) => info!("Loaded environment variables from {}", path.display()),
                Err(e) => {
                    debug!(
                        "Failed to load .env file: {}, using existing environment variables",
                        e
                    );
                }
            }
        }

        // Initialize internal environment variables
        let envs = self.0.get_or_init(|| {
            info!("Initializing global environment variables");
            GlobalEnvsInner::new()
        });

        // Log initialization status
        info!(
            "Global environment variables initialized, secret key length: {}, environment: {}",
            envs.hyper_secret.len(),
            stage
        );
    }

    // Get the current environment name
    pub fn get_environment(&self) -> String {
        let environment = env::var("STAGE").unwrap_or_else(|_| "development".into());
        // Log the environment name
        info!("Current environment: {}", environment);
        environment
    }

    // === Methods to directly access environment variables ===

    // Get secret key
    pub fn secret(&self) -> &str {
        &self.get_inner().hyper_secret
    }

    // Get vault address
    pub fn vault(&self) -> &str {
        &self.get_inner().hyper_vault
    }

    // Check if in development environment
    pub fn is_dev(&self) -> bool {
        let env = self.get_environment();
        env == "dev" || env == "development"
    }

    // Check if in production environment
    pub fn is_production(&self) -> bool {
        let env = self.get_environment();
        env == "prod" || env == "production"
    }

    // Check if in staging environment
    pub fn is_staging(&self) -> bool {
        self.get_environment() == "staging"
    }

    // Check if in unstable environment
    pub fn is_unstable(&self) -> bool {
        self.get_environment() == "unstable"
    }

    // Check if in local environment
    pub fn is_local(&self) -> bool {
        self.get_environment() == "local"
    }

    // Get environment variable with default value
    pub fn get_env_or(&self, key: &str, default: &str) -> String {
        env::var(key).unwrap_or_else(|_| default.to_string())
    }

    // Get required environment variable, panic if not set
    pub fn get_env_required(&self, key: &str) -> String {
        env::var(key).unwrap_or_else(|_| panic!("Required environment variable {} not set", key))
    }
}