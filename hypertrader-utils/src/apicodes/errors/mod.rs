use std::fmt;

pub mod function_path;
use salvo::writing::J<PERSON>;
use serde::{Deserialize, Serialize};
use serde_json::json;

#[allow(non_snake_case)]
pub mod ERROR_CODES {
    include!(concat!(env!("OUT_DIR"), "/error_codes.rs"));
}

#[derive(Serialize, Deserialize)]
pub struct GlobalError {
    pub code: u32,
    pub msg: serde_json::Value,
}

// Henry: The code below will be moved to the hypertrader-dbs crate
// impl From<hypertrader_dbs::DbErr> for GlobalError {
//     fn from(value: hypertrader_dbs::DbErr) -> Self {
//         GlobalError {
//             code: ERROR_CODES::common::DbError,
//             msg: value.to_string().into(),
//         }
//     }
// }
impl From<anyhow::Error> for GlobalError {
    fn from(value: anyhow::Error) -> Self {
        GlobalError {
            code: ERROR_CODES::common::AnyhowError,
            msg: value.to_string().into(),
        }
    }
}

impl From<salvo::http::ParseError> for GlobalError {
    fn from(value: salvo::http::ParseError) -> Self {
        GlobalError {
            code: ERROR_CODES::common::ParseError,
            msg: value.to_string().into(),
        }
    }
}

impl From<serde_json::Error> for GlobalError {
    fn from(value: serde_json::Error) -> Self {
        GlobalError {
            code: ERROR_CODES::common::ParseError,
            msg: value.to_string().into(),
        }
    }
}

impl GlobalError {
    pub fn salvo_header_error(value: &str, method: &str) -> Self {
        GlobalError {
            code: ERROR_CODES::server::SalvoHeaderError,
            msg: json!({
                "error": value,
                "method": method,
            }),
        }
    }

    pub fn depot_error(method: &str) -> Self {
        GlobalError {
            code: ERROR_CODES::server::SalvoDepotError,
            msg: json!({
                "method": method,
            }),
        }
    }
}

#[salvo::async_trait]
impl salvo::Writer for GlobalError {
    async fn write(
        mut self,
        _req: &mut salvo::Request,
        _depot: &mut salvo::Depot,
        res: &mut salvo::Response,
    ) {
        let code = get_first_three_digits(self.code);
        let status_code = salvo::http::StatusCode::from_u16(code as u16).unwrap();

        tracing::error!("Error code: {}, content: {}", status_code, self);

        res.status_code(status_code);
        res.render(Json(self));
    }
}

fn get_first_three_digits(num: u32) -> u32 {
    let mut n = num;
    while n >= 1000 {
        // 确保n是一个三位数
        n /= 10;
    }
    n
}

impl GlobalError {
    pub fn new(code: u32, msg: serde_json::Value) -> Self {
        GlobalError { code, msg }
    }

    pub fn new_code(code: u32) -> Self {
        GlobalError {
            code,
            msg: serde_json::json!({}),
        }
    }
}

impl fmt::Debug for GlobalError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        // 以Debug形式打印code和message
        f.debug_struct("GlobalError")
            .field("code", &self.code)
            .field("message", &self.msg)
            .finish()
    }
}

impl fmt::Display for GlobalError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        // 将message转换为一个字符串，如果失败则显示默认消息
        let msg = self.msg.to_string();
        write!(f, "Error {}: {}", self.code, msg)
    }
}

impl std::error::Error for GlobalError {}

#[test]
fn test_fmt() {
    let t1 = 0;
    let t2 = 999;

    let s = format!("{:0>3}", t1);
    dbg!(s);

    let s = format!("{:0>3}", t2);
    dbg!(s);
}
