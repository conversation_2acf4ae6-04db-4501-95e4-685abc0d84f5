use std::fmt;

pub mod function_path;
use salvo::writing::Json;
use serde::{Deserialize, Serialize};

#[allow(non_snake_case)]
pub mod SUCCESS_CODES {
    include!(concat!(env!("OUT_DIR"), "/success_codes.rs"));
}

#[derive(Serialize, Deserialize)]
pub struct GlobalSuccess {
    pub code: u32,
    pub msg: serde_json::Value,
}

#[salvo::async_trait]
impl salvo::Writer for GlobalSuccess {
    async fn write(
        mut self,
        _req: &mut salvo::Request,
        _depot: &mut salvo::Depot,
        res: &mut salvo::Response,
    ) {
        let code = get_first_three_digits(self.code);
        let status_code = salvo::http::StatusCode::from_u16(code as u16).unwrap();

        tracing::info!("Success code: {}, content: {}", status_code, self);

        res.status_code(status_code);
        res.render(<PERSON><PERSON>(self));
    }
}

fn get_first_three_digits(num: u32) -> u32 {
    let mut n = num;
    while n >= 1000 {
        // 确保n是一个三位数
        n /= 10;
    }
    n
}

impl GlobalSuccess {
    pub fn new(code: u32, msg: serde_json::Value) -> Self {
        GlobalSuccess { code, msg }
    }

    pub fn new_json<T: Serialize>(code: u32, msg: T) -> Self {
        GlobalSuccess {
            code,
            msg: serde_json::json!(msg),
        }
    }

    pub fn new_code(code: u32) -> Self {
        GlobalSuccess {
            code,
            msg: serde_json::json!({}),
        }
    }
}

impl fmt::Debug for GlobalSuccess {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        // 以Debug形式打印code和message
        f.debug_struct("GlobalSuccess")
            .field("code", &self.code)
            .field("message", &self.msg)
            .finish()
    }
}

impl fmt::Display for GlobalSuccess {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        // 将message转换为一个字符串，如果失败则显示默认消息
        let msg = self.msg.to_string();
        write!(f, "Success {}: {}", self.code, msg)
    }
}

#[test]
fn test_fmt() {
    let t1 = 0;
    let t2 = 999;

    let s = format!("{:0>3}", t1);
    dbg!(s);

    let s = format!("{:0>3}", t2);
    dbg!(s);
}
