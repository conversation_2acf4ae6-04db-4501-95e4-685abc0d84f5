use anyhow::Result;
use hypertrader_clickhouse::init_clickhouse_client;
// use hypertrader_data::cex::hyperliquid::HyperliquidDataManager;
// use hypertrader_dbs::init_db;
// use hypertrader_hyperliquid::client::data::models::KlineInterval;
// use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> Result<()> {
    println!("Testing remote ClickHouse cloud service connection...");

    // Try to connect using environment variables
    let clickhouse_client = init_clickhouse_client().await?;

    // Test if connection is successful
    match clickhouse_client.ping().await {
        Ok(true) => println!("Successfully connected to remote ClickHouse cloud service (via environment variables)!"),
        Ok(false) => println!("Failed to connect to remote ClickHouse cloud service!"),
        Err(e) => println!("Error connecting to remote ClickHouse cloud service: {}", e),
    }

    Ok(())
}
