use anyhow::Result;
use clickhouse::{Client, Row};
use hypertrader_models::kline::{interval::KlineInterval, range::KlineRange};
use std::str::FromStr;
use std::{collections::VecDeque, sync::Arc};
use tokio::sync::Mutex;

use hyperliquid_rust_sdk::CandlesSnapshotResponse;
use serde::Deserialize;

use crate::models::kline::{Kline, KlineBatch};

/// ClickHouse client
#[derive(Clone)]
pub struct ClickHouseClient {
    pub client: Arc<Mutex<Client>>,
}

impl ClickHouseClient {
    /// Create a new ClickHouse client
    pub fn new(url: &str) -> Result<Self> {
        let client = Client::default().with_url(url).with_database("dex");

        Ok(Self {
            client: Arc::new(Mutex::new(client)),
        })
    }

    /// Execute a query
    /// This function is used to execute any custom SQL query against the ClickHouse database.
    pub async fn execute_query(&self, query: &str) -> Result<()> {
        let client = self.client.lock().await;
        client.query(query).execute().await?;
        Ok(())
    }

    /// Create a ClickHouse client with basic authentication
    pub fn new_with_auth(url: &str, username: &str, password: &str, clickhouse_db: &str) -> Result<Self> {
        // Use complete ClickHouse URL with port number
        let client = Client::default()
            .with_url(url)
            .with_user(username)
            .with_password(password)
            .with_database(clickhouse_db);

        Ok(Self {
            client: Arc::new(Mutex::new(client)),
        })
    }

    /// Check if the connection is working properly
    pub async fn ping(&self) -> Result<bool> {
        let client = self.client.lock().await;
        // Execute a simple query to check connection
        client.query("SELECT 1").execute().await?;
        Ok(true)
    }

    /// Create kline data table
    pub async fn create_kline_table(&self) -> Result<()> {
        let client = self.client.lock().await;
        let query = r#"
            CREATE TABLE IF NOT EXISTS klines (
                symbol String,
                interval String,
                time_open DateTime64(3),
                time_close DateTime64(3),
                open Float64,
                high Float64,
                low Float64,
                close Float64,
                volume Float64,
                num_trades UInt64
            )
            ENGINE = ReplacingMergeTree()
            PRIMARY KEY (symbol, interval, time_open)
            ORDER BY (symbol, interval, time_open)
        "#;

        client.query(query).execute().await?;
        Ok(())
    }

    /// Batch insert kline data
    pub async fn insert_klines(&self, klines: KlineBatch) -> Result<()> {
        if klines.is_empty() {
            return Ok(());
        }

        // Since CandlesSnapshotResponse doesn't implement Clone, we need to create a new serializable structure
        #[derive(Row, serde::Serialize)]
        struct KlineRow {
            symbol: String,
            interval: String,
            time_open: i64,
            time_close: i64,
            open: f64,
            high: f64,
            low: f64,
            close: f64,
            volume: f64,
            num_trades: u64,
        }

        let client = self.client.lock().await;
        let mut insert = client.insert("klines")?;

        for kline in klines.klines {
            let row = KlineRow {
                symbol: kline.symbol,
                interval: kline.interval,
                time_open: kline.time_open,
                time_close: kline.time_close,
                open: kline.open,
                high: kline.high,
                low: kline.low,
                close: kline.close,
                volume: kline.volume,
                num_trades: kline.num_trades,
            };

            insert.write(&row).await?;
        }

        insert.end().await?;
        Ok(())
    }

    pub async fn insert_ohlc_batch(
        &self,
        table_name: &str,
        klines: KlineBatch,
    ) -> Result<()> {
        if klines.is_empty() {
            return Ok(());
        }

        #[derive(Row, serde::Serialize)]
        struct KlineRow {
            time_open: i64,
            time_close: i64,
            symbol: String,
            open: f64,
            high: f64,
            low: f64,
            close: f64,
            total_volume: f64,
            num_trades: u64,
        }

        let client = self.client.lock().await;
        let mut insert = client.insert(table_name)?;

        for kline in klines.klines {
            let row = KlineRow {
                time_open: kline.time_open,
                time_close: kline.time_close,
                symbol: kline.symbol,
                open: kline.open,
                high: kline.high,
                low: kline.low,
                close: kline.close,
                total_volume: kline.volume,
                num_trades: kline.num_trades,
            };
            // Write the row to the insert
            insert.write(&row).await?;
        }

        insert.end().await?;
        Ok(())
    }

    pub async fn insert_ohlc(
        &self,
        table_name: &str,
        kline: Kline,
    ) -> Result<()> {
        // Since CandlesSnapshotResponse doesn't implement Clone, we need to create a new serializable structure
        #[derive(Row, serde::Serialize)]
        struct KlineRow {
            symbol: String,
            interval: String,
            time_open: i64,
            time_close: i64,
            open: f64,
            high: f64,
            low: f64,
            close: f64,
            volume: f64,
            num_trades: u64,
        }
        // Convert Kline to KlineRow
        let kline_row = KlineRow {
            symbol: kline.symbol,
            interval: kline.interval,
            time_open: kline.time_open,
            time_close: kline.time_close,
            open: kline.open,
            high: kline.high,
            low: kline.low,
            close: kline.close,
            volume: kline.volume,
            num_trades: kline.num_trades,
        };
        let client = self.client.lock().await;
        let query = format!(
            "INSERT INTO {table_name} (time_open, time_close, symbol, open, high, low, close, total_volume, num_trades) VALUES ('{}', '{}', '{}', {}, {}, {}, {}, {}, {})",
            kline_row.time_open, kline_row.time_close, kline_row.symbol, kline_row.open, kline_row.high, kline_row.low, kline_row.close, kline_row.volume, kline_row.num_trades
        );

        client.query(&query).execute().await?;
        Ok(())
    }

    /// Batch store historical kline data
    pub async fn store_klines_history(
        &self,
        klines: Vec<CandlesSnapshotResponse>,
        batch_size: usize,
    ) -> Result<usize> {
        if klines.is_empty() {
            return Ok(0);
        }

        let mut processed = 0;
        let mut queue = VecDeque::new();

        // First convert all data and put into the queue
        for candle in klines {
            let kline = Kline {
                symbol: candle.coin.clone(),
                interval: candle.candle_interval.clone(),
                time_open: candle.time_open as i64,
                time_close: candle.time_close as i64,
                open: candle.open.parse().unwrap_or_default(),
                high: candle.high.parse().unwrap_or_default(),
                low: candle.low.parse().unwrap_or_default(),
                close: candle.close.parse().unwrap_or_default(),
                volume: candle.vlm.parse().unwrap_or_default(),
                num_trades: candle.num_trades,
            };
            queue.push_back(kline);
        }

        // Process data in batches
        while !queue.is_empty() {
            let chunk_size = queue.len().min(batch_size);
            let mut batch = KlineBatch::new();

            for _ in 0..chunk_size {
                if let Some(kline) = queue.pop_front() {
                    batch.add(kline);
                }
            }

            self.insert_klines(batch).await?;
            processed += chunk_size;
        }

        Ok(processed)
    }

    /// Trigger table optimization, call manually when deduplication is needed
    pub async fn optimize_table(&self) -> Result<()> {
        let client = self.client.lock().await;
        client
            .query("OPTIMIZE TABLE klines FINAL")
            .execute()
            .await?;
        Ok(())
    }

    /// Optimize specific table with optional database name
    pub async fn optimize_specific_table(&self, table_name: &str, database: Option<&str>) -> Result<()> {
        let client = self.client.lock().await;
        let query = match database {
            Some(db) => format!("OPTIMIZE TABLE {}.{} FINAL", db, table_name),
            None => format!("OPTIMIZE TABLE {} FINAL", table_name),
        };
        client.query(&query).execute().await?;
        Ok(())
    }

    /// Optimize all tables from 3m to 1M
    pub async fn optimize_all_tables(&self) -> Result<()> {
        let client = self.client.lock().await;
        let timeframes = [
            "3m", "5m", "15m", "30m", "1h", "2h", "4h", "8h", "12h", "1d", "3d", "1w", "1M"
        ];

        for timeframe in timeframes {
            let query = format!("OPTIMIZE TABLE ohlc_{} FINAL", timeframe);
            client.query(&query).execute().await?;
        }
        Ok(())
    }

    /// Get kline data range for a specific symbol and interval
    pub async fn get_kline_range(
        &self,
        symbol: &str,
        interval: &str,
    ) -> Result<Option<KlineRange>> {
        let client = self.client.lock().await;

        // Define structure for receiving results
        #[derive(Debug, Row, Deserialize)]
        struct RangeResult {
            min_time: i64,
            max_time: i64,
            count: u64,
        }

        // Query data range
        let query = format!(
            "SELECT
                MIN(time_open) as min_time,
                MAX(time_open) as max_time,
                COUNT(*) as count
             FROM klines
             WHERE symbol = '{}' AND interval = '{}'",
            symbol, interval
        );

        // Use cursor to get results, not fetch_one
        let mut cursor = client.query(&query).fetch::<RangeResult>()?;
        let result = cursor.next().await?;

        // Check if there is data
        if let Some(result) = result {
            if result.count == 0 {
                return Ok(None);
            }

            return Ok(Some(KlineRange {
                symbol: symbol.to_string(),
                interval: KlineInterval::from_str(interval)?,
                start_time: result.min_time,
                end_time: result.max_time,
                count: result.count,
            }));
        }

        Ok(None)
    }

    /// Get kline data ranges for all symbols
    pub async fn get_all_kline_ranges(&self) -> Result<Vec<KlineRange>> {
        let client = self.client.lock().await;

        // Define structure for receiving results
        #[derive(Debug, Row, Deserialize)]
        struct RangeResult {
            symbol: String,
            interval: String,
            min_time: i64,
            max_time: i64,
            count: u64,
        }

        // Query data ranges for all symbols and intervals
        let query = "SELECT
                symbol,
                interval,
                MIN(time_open) as min_time,
                MAX(time_open) as max_time,
                COUNT(*) as count
             FROM klines
             GROUP BY symbol, interval";

        let mut ranges = Vec::new();
        let mut cursor = client.query(query).fetch::<RangeResult>()?;

        while let Some(result) = cursor.next().await? {
            if result.count > 0 {
                ranges.push(KlineRange {
                    symbol: result.symbol,
                    interval: KlineInterval::from_str(&result.interval)?,
                    start_time: result.min_time,
                    end_time: result.max_time,
                    count: result.count,
                });
            }
        }

        Ok(ranges)
    }

    pub async fn create_ohlc_table_and_view(
        &self,
        interval_name: &str,
        interval_duration: &str,
        interval_unit: &str
    ) -> Result<()> {
        let client = self.client.lock().await;
        // Create table
        let table_name = format!("ohlc_{}", interval_name);
        if interval_name != "1m" {
        let create_table_query = format!(
                "CREATE TABLE IF NOT EXISTS {} (
                    time_open DateTime64(3),
                    time_close DateTime64(3),
                    symbol String,
                    open AggregateFunction(argMin, Float64, DateTime64(3)),
                    high SimpleAggregateFunction(max, Float64),
                    low SimpleAggregateFunction(min, Float64),
                    close AggregateFunction(argMax, Float64, DateTime64(3)),
                    total_volume SimpleAggregateFunction(sum, Float64),
                    num_trades SimpleAggregateFunction(sum, UInt64)
                ) ENGINE = AggregatingMergeTree
                ORDER BY (time_open, time_close, symbol)
                PARTITION BY toYYYYMM(time_open)
                SETTINGS index_granularity = 8192",
            table_name
            );
            client.query(&create_table_query).execute().await?;
        } else {
            // Create table for 1m interval
            let create_table_query = format!(
                "CREATE TABLE IF NOT EXISTS {} (
                    time_open DateTime64(3) CODEC(Delta, ZSTD),
                    time_close DateTime64(3) CODEC(Delta, ZSTD),
                    symbol String CODEC(ZSTD),
                    open Float64 CODEC(ZSTD),
                    high Float64 CODEC(ZSTD),
                    low Float64 CODEC(ZSTD),
                    close Float64 CODEC(ZSTD),
                    total_volume Float64 CODEC(ZSTD),
                    num_trades UInt64 CODEC(ZSTD),
                ) ENGINE = ReplacingMergeTree()
                ORDER BY (time_open, time_close, symbol)
                PARTITION BY toYYYYMM(time_open)
                SETTINGS index_granularity = 8192",
            table_name
            );
            client.query(&create_table_query).execute().await?;
        }

        // Not process with the view creation if the interval is 1 minute
        // Because 1 minute is the source of the data
        if interval_name != "1m" {
            // Adjust time close expression based on interval unit
            // The error occurs because subtractMilliseconds cannot be used with Date type (which happens with week intervals)
            let time_close_expr = match interval_unit {
                "WEEK" | "MONTH" => {
                    // For week and month intervals, subtract 1 second instead of 1 millisecond
                    // This avoids Date type issues while still giving the end of the period
                    format!("toStartOfInterval(toDateTime64(time_open, 3), INTERVAL {interval_duration} {interval_unit}) + INTERVAL {interval_duration} {interval_unit} - INTERVAL 1 SECOND")
                },
                _ => {
                    // For other intervals (minute, hour, day), use the millisecond precision approach
                    format!("toStartOfInterval(toDateTime64(time_open, 3), INTERVAL {interval_duration} {interval_unit}) + INTERVAL {interval_duration} {interval_unit} - INTERVAL 1 MILLISECOND")
                }
            };

            // Create materialized view
            let view_name = format!("mv_ohlc_{interval_name}");
            let create_view_query = format!(
                "CREATE MATERIALIZED VIEW IF NOT EXISTS {view_name} TO {table_name} AS
            SELECT
                toStartOfInterval(time_open, INTERVAL {interval_duration} {interval_unit}) AS time_open,
                {time_close_expr} AS time_close,
                symbol,
                argMinState(open, toDateTime64(ohlc_1m.time_open, 3,'UTC')) AS open,
                max(high) AS high,
                min(low) AS low,
                argMaxState(close, toDateTime64(ohlc_1m.time_open, 3,'UTC')) AS close,
                sum(total_volume) AS total_volume,
                sum(num_trades) AS num_trades
            FROM ohlc_1m
            GROUP BY time_open, time_close, symbol",
            );

            client.query(&create_view_query).execute().await?;
        }

        Ok(())
    }
}
