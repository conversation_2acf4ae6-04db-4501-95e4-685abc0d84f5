pub mod client;
pub mod models;

pub use client::ClickHouseClient;
use hypertrader_utils::env::GLOBAL_ENVS;

// 为了向后兼容保留的函数
pub async fn init_clickhouse_client() -> anyhow::Result<ClickHouseClient> {
    let clickhouse_url = GLOBAL_ENVS.clickhouse_url();
    let key_id = GLOBAL_ENVS.clickhouse_key_id();
    let key_secret = GLOBAL_ENVS.clickhouse_key_secret();
    let clickhouse_db = GLOBAL_ENVS.clickhouse_db();

    match (key_id, key_secret) {
        (Some(id), Some(secret)) => init_clickhouse_client_remote(clickhouse_url.as_str(), id, secret, clickhouse_db.unwrap_or("default")).await,
        _ => init_clickhouse_client_local().await,
    }
}

async fn init_clickhouse_client_local() -> anyhow::Result<ClickHouseClient> {
    // let clickhouse_url = "http://localhost:8123"; // ClickHouse服务器地址
    let clickhouse_url = "http://jiuzhuang1987.local:8123"; // ClickHouse服务器地址
    let clickhouse_client = ClickHouseClient::new(clickhouse_url)?;

    Ok(clickhouse_client)
}

async fn init_clickhouse_client_remote(
    clickhouse_url: &str,
    key_id: &str,
    key_secret: &str,
    clickhouse_db: &str,
) -> anyhow::Result<ClickHouseClient> {
    // 创建带认证的ClickHouse客户端
    let clickhouse_client = ClickHouseClient::new_with_auth(clickhouse_url, key_id, key_secret, clickhouse_db)?;

    Ok(clickhouse_client)
}
//
