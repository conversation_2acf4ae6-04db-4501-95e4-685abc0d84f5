use hyperliquid_rust_sdk::CandlesSnapshotResponse;
use serde::{Deserialize, Serialize};

/// K线数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Kline {
    pub symbol: String,
    pub interval: String,
    pub time_open: i64,
    pub time_close: i64,
    pub open: f64,
    pub high: f64,
    pub low: f64,
    pub close: f64,
    pub volume: f64,
    pub num_trades: u64,
}

impl From<CandlesSnapshotResponse> for Kline {
    fn from(candle: CandlesSnapshotResponse) -> Self {
        Self {
            symbol: candle.coin,
            interval: candle.candle_interval,
            time_open: candle.time_open as i64,
            time_close: candle.time_close as i64,
            open: candle.open.parse().unwrap_or_default(),
            high: candle.high.parse().unwrap_or_default(),
            low: candle.low.parse().unwrap_or_default(),
            close: candle.close.parse().unwrap_or_default(),
            volume: candle.vlm.parse().unwrap_or_default(),
            num_trades: candle.num_trades,
        }
    }
}

/// K线批量数据
#[derive(Debug, <PERSON>lone)]
pub struct KlineBatch {
    pub klines: Vec<Kline>,
}

impl KlineBatch {
    /// 创建新的K线批量数据
    pub fn new() -> Self {
        Self { klines: Vec::new() }
    }

    /// 从CandlesSnapshotResponse向量创建K线批量数据
    pub fn from_candles(candles: Vec<CandlesSnapshotResponse>) -> Self {
        let klines = candles.into_iter().map(Kline::from).collect();
        Self { klines }
    }

    /// 添加单个K线数据
    pub fn add(&mut self, kline: Kline) {
        self.klines.push(kline);
    }

    /// 添加多个K线数据
    pub fn add_batch(&mut self, klines: Vec<Kline>) {
        self.klines.extend(klines);
    }

    /// 获取批量大小
    pub fn len(&self) -> usize {
        self.klines.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.klines.is_empty()
    }

    /// Convert batch to Vec<Kline> for OHLC format
    pub fn to_vec(&self) -> Vec<Kline> {
        self.klines.clone()
    }
}

impl Default for KlineBatch {
    fn default() -> Self {
        Self::new()
    }
}
