# Hypertrader ClickHouse Module

This module provides functionality for interacting with ClickHouse database, primarily for storing and querying historical candlestick (kline) data.

## Features

- Connect to ClickHouse database
- Create table structures for candlestick data
- Bulk insert candlestick data
- Efficiently process large volumes of historical candlestick data

## Dependencies

- clickhouse = "0.13.2"
- tokio
- serde
- anyhow
- chrono

## Usage Examples

### Initialize Client

```rust
use hypertrader_clickhouse::client::ClickHouseClient;

// Create ClickHouse client
let clickhouse_url = "http://localhost:8123"; // ClickHouse server address
let clickhouse_client = ClickHouseClient::new(clickhouse_url)?;
```

### Create Candlestick Data Table

```rust
// Create kline data table (if it doesn't exist)
clickhouse_client.create_kline_table().await?;
```

### Bulk Insert Candlestick Data

```rust
use hypertrader_clickhouse::models::kline::KlineBatch;

// Get candlestick data
let klines = hyperliquid_data_manager
    .client
    .klines_history(symbol.to_string(), start_time, end_time, interval)
    .await?;

// Bulk insert candlestick data into ClickHouse
let batch_size = 1000; // Number of klines to process per batch
let processed = clickhouse_client.store_klines_history(klines, batch_size).await?;
println!("Data insertion complete! Successfully processed {} records", processed);
```

## Table Structure

The structure of the candlestick data table (`klines`) is as follows:

```sql
CREATE TABLE IF NOT EXISTS klines (
    symbol String,           -- Trading pair symbol
    interval String,         -- Candlestick time interval (1m, 1h etc.)
    time_open DateTime64(3), -- Open time (milliseconds)
    time_close DateTime64(3),-- Close time (milliseconds)
    open Float64,            -- Open price
    high Float64,            -- High price
    low Float64,             -- Low price
    close Float64,           -- Close price
    volume Float64,          -- Volume
    num_trades UInt64        -- Number of trades
)
ENGINE = MergeTree()
ORDER BY (symbol, interval, time_open)
```

## ClickHouse Configuration

Ensure your ClickHouse server is properly configured and running. By default, the ClickHouse HTTP interface listens on port 8123.