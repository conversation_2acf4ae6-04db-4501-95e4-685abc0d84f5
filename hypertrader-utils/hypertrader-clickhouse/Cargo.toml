[package]
name = "hypertrader-clickhouse"
version = "0.1.0"
edition = "2024"

[dependencies]
hypertrader-models = { workspace = true }
hypertrader-utils = { workspace = true }

clickhouse = { workspace = true }
hyperliquid_rust_sdk = { workspace = true }
anyhow = "1.0"
tokio = { version = "1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
chrono = "0.4"
serde_json = { workspace = true }
tracing = { workspace = true }
