use serde_json::Value;
use std::env;
use std::fs::{self, File};
use std::io::Write;
use std::path::Path;

fn gen_error_codes() -> Result<(), std::io::Error> {
    println!("cargo:rerun-if-changed=src/apicodes/errors/errors.json");

    let error_codes = fs::read_to_string("src/apicodes/errors/errors.json")?;
    let error_codes: Value = serde_json::from_str(&error_codes).unwrap();

    let out_dir = env::var("OUT_DIR").unwrap();
    let dest_path = Path::new(&out_dir).join("error_codes.rs");
    let mut f = File::create(dest_path)?;

    // 初始化存储错误消息的向量
    let mut messages_map = Vec::new();

    // 为每个错误类别生成一个模块
    for (category, info) in error_codes["errors"].as_object().unwrap() {
        // 在生成的文件顶部添加 allow 属性
        writeln!(f, "#[allow(non_upper_case_globals)]")?;
        writeln!(f, "pub mod {} {{", category)?;

        for (code_suffix, error) in info["messages"].as_object().unwrap() {
            let name = error["name"].as_str().unwrap();
            // 确保前缀和后缀组合达到9位的总长度
            let full_code = format!("{}{:0>3}", info["prefix"].as_str().unwrap(), code_suffix);
            let description = error["description"].as_str().unwrap();

            // 在生成的文件顶部添加 allow 属性
            writeln!(f, "#[allow(non_upper_case_globals)]")?;
            // 在模块内部定义常量
            writeln!(f, "    pub const {}: u32 = {};", name, full_code)?;

            // 准备全局 message 函数的 match 分支
            messages_map.push(format!("            {} => \"{}\",", full_code, description));
        }

        writeln!(f, "}}")?; // 关闭模块
    }

    // 生成全局的 message 函数
    writeln!(f, "pub fn message(code: u32) -> &'static str {{")?;
    writeln!(f, "    match code {{")?;
    for entry in messages_map {
        writeln!(f, "{}", entry)?;
    }
    writeln!(f, "        _ => \"未知错误\",")?;
    writeln!(f, "    }}")?;
    writeln!(f, "}}")?;

    Ok(())
}

fn gen_success_codes() -> Result<(), std::io::Error> {
    println!("cargo:rerun-if-changed=src/apicodes/success/success.json");

    let success_codes = fs::read_to_string("src/apicodes/success/success.json")?;
    let success_codes: Value = serde_json::from_str(&success_codes).unwrap();

    let out_dir = env::var("OUT_DIR").unwrap();
    let dest_path = Path::new(&out_dir).join("success_codes.rs");
    let mut f = File::create(dest_path)?;

    // 初始化存储错误消息的向量
    let mut messages_map = Vec::new();

    // 为每个错误类别生成一个模块
    for (category, info) in success_codes["success"].as_object().unwrap() {
        // 在生成的文件顶部添加 allow 属性
        writeln!(f, "#[allow(non_upper_case_globals)]")?;
        writeln!(f, "pub mod {} {{", category)?;

        for (code_suffix, success) in info["messages"].as_object().unwrap() {
            let name = success["name"].as_str().unwrap();
            // 确保前缀和后缀组合达到9位的总长度
            let full_code = format!("{}{:0>3}", info["prefix"].as_str().unwrap(), code_suffix);
            let description = success["description"].as_str().unwrap();

            // 在生成的文件顶部添加 allow 属性
            writeln!(f, "#[allow(non_upper_case_globals)]")?;
            // 在模块内部定义常量
            writeln!(f, "    pub const {}: u32 = {};", name, full_code)?;

            // 准备全局 message 函数的 match 分支
            messages_map.push(format!("            {} => \"{}\",", full_code, description));
        }

        writeln!(f, "}}")?; // 关闭模块
    }

    // 生成全局的 message 函数
    writeln!(f, "pub fn message(code: u32) -> &'static str {{")?;
    writeln!(f, "    match code {{")?;
    for entry in messages_map {
        writeln!(f, "{}", entry)?;
    }
    writeln!(f, "        _ => \"未知成功\",")?;
    writeln!(f, "    }}")?;
    writeln!(f, "}}")?;

    Ok(())
}

fn main() {
    let _ = gen_error_codes();
    let _ = gen_success_codes();
}
