# Version control
.git
.gitignore
.github

# Editor files
.idea
.vscode
*.swp
*.swo
*~

# Rust build artifacts
target/
**/*.rs.bk
*.pdb

# Environment files (use .env.example instead)
.env
.env.local

# Documentation and non-essential files
docs/
README.md
LICENSE
CHANGELOG.md
CONTRIBUTING.md

# Development tools
.rustfmt.toml
.clippy.toml

# Docker files (not needed within the build context)
docker-compose*.yml

# Test data and caches
**/__pycache__/
**/*.py[cod]
**/*$py.class
**/.pytest_cache/
**/node_modules/
**/.npm
**/.yarn

# Log files
**/*.log
logs/

# Backup files
**/*.bak
**/*.backup
**/*.old
**/*.orig

# Data directories (mounted as volumes)
data/

# Package manager files (except for Cargo.lock which is needed)
bun.lockb
package-lock.json
yarn.lock
Cargo.lock.bak

# Build artifacts
dist/
build/