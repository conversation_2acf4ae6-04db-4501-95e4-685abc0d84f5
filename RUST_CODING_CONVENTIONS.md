# XBit HyperTrader Rust Coding Conventions

## Tech Stack
- Rust 1.70+
- Salvo Web Framework
- SeaORM Database ORM
- Tokio Async Runtime
- ClickHouse Time-Series Database
- PostgreSQL Primary Database
- WebSocket Real-time Communication
- Serde JSON Serialization
- Anyhow/Thiserror Error Handling
- Tracing Logging Framework

## Table of Contents
1. [Project Structure](#project-structure)
2. [Naming Conventions](#naming-conventions)
3. [Module Organization](#module-organization)
4. [Documentation Standards](#documentation-standards)
5. [Error Handling Patterns](#error-handling-patterns)
6. [Async/Await Conventions](#asyncawait-conventions)
7. [Database Patterns](#database-patterns)
8. [WebSocket Message Handling](#websocket-message-handling)
9. [API Endpoint Design](#api-endpoint-design)
10. [Security Practices](#security-practices)
11. [Testing Patterns](#testing-patterns)
12. [Code Quality Standards](#code-quality-standards)
13. [Environment Management](#environment-management)
14. [Performance Guidelines](#performance-guidelines)
15. [Git Workflow](#git-workflow)

## Project Structure

```
src/
├── handlers/              # Request handlers organized by feature
│   ├── dev/              # Development and testing endpoints
│   │   ├── service/      # Business logic services
│   │   └── db/           # Database operations
│   ├── monitor/          # Address monitoring functionality
│   ├── ws/               # WebSocket handlers and services
│   │   ├── client.rs     # Client connection management
│   │   ├── manager.rs    # Connection pool management
│   │   ├── greeting/     # Greeting service example
│   │   ├── notifier/     # Notification service
│   │   └── types.rs      # WebSocket message types
│   ├── rest/             # REST API handlers
│   └── state.rs          # Application state management
├── router.rs             # Route configuration and middleware
├── main.rs               # Application entry point
└── lib.rs                # Library exports

hypertrader-utils/        # Shared utilities workspace
├── hypertrader-dbs/      # Database schemas and managers
├── hypertrader-clickhouse/ # ClickHouse integration
├── src/
│   ├── apicodes/         # API response codes
│   ├── env/              # Environment variable management
│   ├── init/             # Application initialization
│   └── logger/           # Logging configuration

hypertrader-data/         # Data management layer
hypertrader-hyperliquid/  # Hyperliquid API integration
hypertrader-models/       # Shared data models
hypertrader-services/     # Background services
```

## Naming Conventions

### Rust Standard Conventions
- **snake_case** for:
  - Variables: `user_id`, `market_data`
  - Functions: `get_all_users()`, `process_limit_orders()`
  - Module names: `contract_service`, `ws_handler`
  - File names: `contract_service.rs`, `ws_manager.rs`

- **PascalCase** for:
  - Types/Structs: `AppState`, `WsMessage`, `ContractService`
  - Enums: `WsMessage`, `NotificationPriority`
  - Traits: `DatabaseConnection`, `MessageHandler`

- **SCREAMING_SNAKE_CASE** for:
  - Constants: `SUCCESS_CODES`, `HEARTBEAT_INTERVAL_MS`
  - Static variables: `WS_MANAGER`, `GLOBAL_ENVS`

### Project-Specific Conventions
- **API Endpoints**: Use descriptive paths with consistent patterns
  - GET `/apis/v0/dev/cs/get_all_users` ✅
  - GET `/users` ❌ (too generic for this context)

- **WebSocket Events**: Use `event_name` format
  - `greeting_request`, `notification_update` ✅
  - `greetingRequest`, `NotificationUpdate` ❌

- **Database Tables**: Use singular nouns with underscores
  - `user`, `trade_order`, `vault_position` ✅
  - `users`, `tradeOrders` ❌

## Module Organization

### Visibility Rules
```rust
// Public API - carefully consider what to expose
pub mod handlers;
pub mod router;

// Internal modules - keep implementation details private
mod internal_utils;
mod private_helpers;

// Re-exports for clean public API
pub use handlers::ws::{WsMessage, WsClient};
pub use handlers::state::AppState;
```

### Import Organization
```rust
// 1. Standard library imports
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

// 2. External crate imports (alphabetical)
use anyhow::Result;
use salvo::{handler, Request, Response};
use serde::{Deserialize, Serialize};
use tokio::sync::Mutex;
use tracing::{debug, error, info};

// 3. Internal crate imports
use hypertrader_dbs::{DatabaseConnection, DbErr};
use hypertrader_utils::apicodes::errors::GlobalError;

// 4. Local module imports
use crate::handlers::state::AppState;
use super::types::WsMessage;
```

### Module Structure Example
```rust
// src/handlers/ws/mod.rs
pub mod client;
pub mod greeting;
pub mod handlers;
pub mod manager;
pub mod notifier;
pub mod types;

// Clean re-exports
pub use client::WsClient;
pub use handlers::{handle_socket, ws_handler};
pub use manager::{WsManager, WS_MANAGER};
pub use types::WsMessage;

// Global instances with lazy initialization
lazy_static::lazy_static! {
    pub static ref NOTIFICATION_SERVICE: notifier::NotificationService =
        notifier::NotificationService::new();
}
```

## Documentation Standards

### Current Issues (Rating: 3/10)
**Problems in Current Codebase:**
```rust
// BAD: No documentation, unclear purpose
#[handler]
async fn get_all_orders(req: &mut Request, res: &mut Response) -> Result<(),GlobalError>  {
    let app_state = AppState::new().await;
    // let orders = order::find().all(app_state.db.as_ref()).await;
    // ... 10+ lines of commented code
    Ok(())
}
```

**Required Documentation Standards:**
```rust
/// Retrieves all active trading orders for the current user.
///
/// This endpoint returns a paginated list of orders with their current status,
/// including pending, filled, and cancelled orders from the last 30 days.
///
/// # Arguments
/// * `req` - HTTP request containing optional query parameters for filtering
/// * `res` - HTTP response that will contain the order list or error
///
/// # Returns
/// * `Ok(())` - Orders successfully retrieved and written to response
/// * `Err(GlobalError)` - Database connection failed or user unauthorized
///
/// # Example Response
/// ```json
/// {
///   "code": 200,
///   "msg": "Success",
///   "data": [
///     {
///       "id": "order_123",
///       "symbol": "BTC",
///       "side": "buy",
///       "quantity": 0.1,
///       "price": 45000.0,
///       "status": "filled"
///     }
///   ]
/// }
/// ```
#[handler]
pub async fn get_all_orders(
    req: &mut Request,
    res: &mut Response
) -> Result<(), GlobalError> {
    let app_state = req.extensions()
        .get::<AppState>()
        .ok_or_else(|| GlobalError::internal_error("App state not found"))?;

    let orders = order::Entity::find()
        .filter(order::Column::UserId.eq(get_current_user_id(req)?))
        .filter(order::Column::CreatedAt.gte(thirty_days_ago()))
        .all(&app_state.db)
        .await
        .map_err(GlobalError::from)?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::order::GetOrdersSuccess,
        orders,
    )));

    Ok(())
}
```

### Module Documentation
```rust
//! # WebSocket Handler Module
//!
//! This module provides real-time WebSocket communication for the HyperTrader platform.
//! It handles client connections, message routing, heartbeat monitoring, and service
//! integration for greeting and notification systems.
//!
//! ## Features
//! - Automatic client connection management with heartbeat monitoring
//! - Message routing based on type and event patterns
//! - Integration with greeting and notification services
//! - Graceful connection cleanup and error handling
//!
//! ## Message Flow
//! 1. Client connects via `/ws` endpoint with optional client_id
//! 2. Server sends welcome message with assigned client_id
//! 3. Client can send various message types (Custom, Subscribe, Ping, etc.)
//! 4. Server routes messages to appropriate handlers
//! 5. Server sends responses and broadcasts as needed
//!
//! ## Example Usage
//! ```rust
//! use crate::handlers::ws::{ws_handler, WsMessage};
//!
//! // In router configuration
//! Router::with_path("/ws").goal(ws_handler)
//! ```

use std::time::{Duration, Instant};
// ... rest of module
```

### Struct and Enum Documentation
```rust
/// Represents a WebSocket client connection with associated metadata.
///
/// Each client maintains its own message sender channel, heartbeat timing,
/// and subscription list for efficient message routing and connection management.
#[derive(Clone)]
pub struct WsClient {
    /// Unique identifier for this client connection
    pub id: String,
    /// Channel sender for outbound messages to this client
    pub sender: UnboundedSender<Message>,
    /// Timestamp of the last successful ping/pong exchange
    pub last_ping: Instant,
    /// List of channels/topics this client is subscribed to
    pub subscriptions: Vec<String>,
}

/// WebSocket message types supported by the HyperTrader platform.
///
/// Messages use a tagged union pattern with type-specific data payloads.
/// All messages are JSON-serialized for transmission over the WebSocket.
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(tag = "type", content = "data")]
pub enum WsMessage {
    /// Initial connection acknowledgment with assigned client ID
    Connect { client_id: String },

    /// Subscribe to real-time data for specific symbols/channels
    Subscribe {
        channel: String,
        symbols: Vec<String>
    },

    /// Real-time market data update
    MarketData {
        symbol: String,
        price: f64,
        volume: f64
    },

    /// Server-to-client heartbeat check
    Ping,

    /// Client-to-server heartbeat response
    Pong,

    /// Custom event with flexible data payload for service extensions
    Custom {
        event: String,
        data: serde_json::Value
    },
}
```

## Error Handling Patterns

### Current Issues (Rating: 5/10)
** Problems in Current Codebase:**
```rust
// BAD: Generic error conversion without context
.map_err(|_| GlobalError::depot_error(stdext::function_name!()))?;

// BAD: Unwrap in production code
let users: Vec<user::Model> = user::Entity::find().all(&self.db).await.unwrap();

// BAD: Silent error handling
match orders {
    Ok(orders) => res.render(Json(orders)),
    Err(err) => {
        // Just log and continue - user gets no feedback
        tracing::error!("Database error: {}", err);
    }
}
```

** Required Error Handling Standards:**
```rust
/// Custom error types for domain-specific errors
#[derive(Debug, thiserror::Error)]
pub enum HyperTraderError {
    #[error("Database operation failed: {message}")]
    Database {
        message: String,
        #[source]
        source: sea_orm::DbErr
    },

    #[error("User {user_id} not authorized for operation: {operation}")]
    Unauthorized {
        user_id: String,
        operation: String
    },

    #[error("Invalid market data: {symbol} price {price} is out of range")]
    InvalidMarketData {
        symbol: String,
        price: f64
    },

    #[error("WebSocket connection {client_id} failed: {reason}")]
    WebSocketError {
        client_id: String,
        reason: String
    },
}

/// Convert domain errors to API responses
impl From<HyperTraderError> for GlobalError {
    fn from(err: HyperTraderError) -> Self {
        match err {
            HyperTraderError::Database { message, .. } => {
                GlobalError::new(
                    ERROR_CODES::common::DbError,
                    format!("Database operation failed: {}", message)
                )
            }
            HyperTraderError::Unauthorized { user_id, operation } => {
                GlobalError::new(
                    ERROR_CODES::auth::Unauthorized,
                    format!("User {} not authorized for {}", user_id, operation)
                )
            }
            // ... other conversions
        }
    }
}

/// Proper error handling in handlers
#[handler]
pub async fn get_user_orders(
    req: &mut Request,
    res: &mut Response,
) -> Result<(), GlobalError> {
    let user_id = extract_user_id(req)
        .map_err(|e| HyperTraderError::Unauthorized {
            user_id: "unknown".to_string(),
            operation: "get_orders".to_string(),
        })?;

    let app_state = req.extensions()
        .get::<AppState>()
        .ok_or_else(|| GlobalError::internal_error("App state not initialized"))?;

    let orders = order::Entity::find()
        .filter(order::Column::UserId.eq(user_id))
        .all(&app_state.db)
        .await
        .map_err(|db_err| HyperTraderError::Database {
            message: format!("Failed to fetch orders for user {}", user_id),
            source: db_err,
        })?;

    res.status_code(StatusCode::OK);
    res.render(Json(GlobalSuccess::new_json(
        SUCCESS_CODES::order::GetOrdersSuccess,
        orders,
    )));

    Ok(())
}
```

### Result Type Patterns
```rust
/// Use Result for operations that can fail
type HyperResult<T> = Result<T, HyperTraderError>;

/// Chain operations with proper error context
pub async fn process_market_order(
    order_request: MarketOrderRequest,
    db: &DatabaseConnection,
) -> HyperResult<OrderResponse> {
    // Validate input
    validate_order_request(&order_request)
        .map_err(|e| HyperTraderError::InvalidMarketData {
            symbol: order_request.symbol.clone(),
            price: order_request.price,
        })?;

    // Check user authorization
    let user = get_user_by_id(&order_request.user_id, db)
        .await
        .map_err(|e| HyperTraderError::Database {
            message: format!("Failed to fetch user {}", order_request.user_id),
            source: e,
        })?
        .ok_or_else(|| HyperTraderError::Unauthorized {
            user_id: order_request.user_id.clone(),
            operation: "place_market_order".to_string(),
        })?;

    // Execute order
    let order = execute_order(order_request, &user, db)
        .await
        .map_err(|e| HyperTraderError::Database {
            message: "Failed to execute market order".to_string(),
            source: e,
        })?;

    Ok(OrderResponse::from(order))
}
```

## Async/Await Conventions

### Tokio Runtime Patterns
```rust
/// Proper async function signatures
pub async fn handle_websocket_connection(
    ws: WebSocket,
    client_id: String,
    manager: Arc<WsManager>,
) -> Result<(), HyperTraderError> {
    // Split WebSocket for concurrent read/write
    let (ws_sender, mut ws_receiver) = ws.split();

    // Create channels for message passing
    let (tx, rx) = mpsc::unbounded_channel();
    let rx = UnboundedReceiverStream::new(rx);

    // Register client with manager
    manager.register_client(client_id.clone(), tx.clone());

    // Spawn sender task
    let sender_task = tokio::spawn(async move {
        if let Err(e) = rx.map(|m| Ok::<_, SalvoError>(m))
            .forward(ws_sender)
            .await
        {
            tracing::error!("WebSocket send error: {}", e);
        }
    });

    // Handle incoming messages
    while let Some(msg_result) = ws_receiver.next().await {
        match msg_result {
            Ok(msg) if msg.is_text() => {
                if let Ok(text) = msg.as_str() {
                    process_text_message(&client_id, &tx, text.to_owned()).await;
                }
            }
            Ok(msg) if msg.is_close() => {
                tracing::info!("Client {} requested close", client_id);
                break;
            }
            Err(e) => {
                tracing::error!("WebSocket receive error: {}", e);
                break;
            }
            _ => {} // Handle other message types
        }
    }

    // Cleanup
    manager.remove_client(&client_id);
    sender_task.abort();

    Ok(())
}
```

### Concurrent Operations
```rust
/// Use join! for concurrent independent operations
pub async fn get_dashboard_data(
    user_id: &str,
    db: &DatabaseConnection,
) -> HyperResult<DashboardData> {
    let (orders_result, positions_result, balance_result) = tokio::join!(
        get_user_orders(user_id, db),
        get_user_positions(user_id, db),
        get_user_balance(user_id, db)
    );

    let orders = orders_result?;
    let positions = positions_result?;
    let balance = balance_result?;

    Ok(DashboardData {
        orders,
        positions,
        balance,
        last_updated: Utc::now(),
    })
}

/// Use select! for racing operations
pub async fn wait_for_order_fill_or_timeout(
    order_id: &str,
    timeout: Duration,
) -> HyperResult<OrderStatus> {
    tokio::select! {
        result = wait_for_order_fill(order_id) => {
            result
        }
        _ = tokio::time::sleep(timeout) => {
            Err(HyperTraderError::Timeout {
                operation: format!("wait_for_order_fill({})", order_id),
                timeout_ms: timeout.as_millis() as u64,
            })
        }
    }
}
```

## Database Patterns

### SeaORM Best Practices
```rust
/// Entity definitions with proper relationships
use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "trade_orders")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub user_id: i32,
    pub symbol: String,
    pub order_type: OrderType,
    pub side: OrderSide,
    pub quantity: Decimal,
    pub price: Option<Decimal>,
    pub status: OrderStatus,
    pub created_at: DateTimeUtc,
    pub updated_at: DateTimeUtc,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::UserId",
        to = "super::user::Column::Id"
    )]
    User,
}

impl Related<super::user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

/// Database manager with proper error handling
pub struct OrderManager {
    db: DatabaseConnection,
}

impl OrderManager {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }

    /// Create a new order with validation
    pub async fn create_order(
        &self,
        order_request: CreateOrderRequest,
    ) -> HyperResult<order::Model> {
        // Validate request
        self.validate_order_request(&order_request).await?;

        // Create active model
        let order = order::ActiveModel {
            user_id: Set(order_request.user_id),
            symbol: Set(order_request.symbol),
            order_type: Set(order_request.order_type),
            side: Set(order_request.side),
            quantity: Set(order_request.quantity),
            price: Set(order_request.price),
            status: Set(OrderStatus::Pending),
            created_at: Set(Utc::now()),
            updated_at: Set(Utc::now()),
            ..Default::default()
        };

        // Insert with transaction
        let txn = self.db.begin().await
            .map_err(|e| HyperTraderError::Database {
                message: "Failed to start transaction".to_string(),
                source: e,
            })?;

        let result = order.insert(&txn).await
            .map_err(|e| HyperTraderError::Database {
                message: "Failed to insert order".to_string(),
                source: e,
            })?;

        txn.commit().await
            .map_err(|e| HyperTraderError::Database {
                message: "Failed to commit transaction".to_string(),
                source: e,
            })?;

        Ok(result)
    }

    /// Get orders with pagination and filtering
    pub async fn get_user_orders(
        &self,
        user_id: i32,
        filters: OrderFilters,
        pagination: Pagination,
    ) -> HyperResult<(Vec<order::Model>, u64)> {
        let mut query = order::Entity::find()
            .filter(order::Column::UserId.eq(user_id));

        // Apply filters
        if let Some(symbol) = filters.symbol {
            query = query.filter(order::Column::Symbol.eq(symbol));
        }

        if let Some(status) = filters.status {
            query = query.filter(order::Column::Status.eq(status));
        }

        if let Some(from_date) = filters.from_date {
            query = query.filter(order::Column::CreatedAt.gte(from_date));
        }

        // Get total count for pagination
        let total = query.clone().count(&self.db).await
            .map_err(|e| HyperTraderError::Database {
                message: "Failed to count orders".to_string(),
                source: e,
            })?;

        // Apply pagination and ordering
        let orders = query
            .order_by_desc(order::Column::CreatedAt)
            .offset(pagination.offset())
            .limit(pagination.limit)
            .all(&self.db)
            .await
            .map_err(|e| HyperTraderError::Database {
                message: "Failed to fetch orders".to_string(),
                source: e,
            })?;

        Ok((orders, total))
    }
}
```

### Migration Patterns
```rust
/// Database migrations with proper versioning
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(TradeOrders::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(TradeOrders::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(TradeOrders::UserId).integer().not_null())
                    .col(ColumnDef::new(TradeOrders::Symbol).string().not_null())
                    .col(ColumnDef::new(TradeOrders::OrderType).string().not_null())
                    .col(ColumnDef::new(TradeOrders::Side).string().not_null())
                    .col(ColumnDef::new(TradeOrders::Quantity).decimal().not_null())
                    .col(ColumnDef::new(TradeOrders::Price).decimal())
                    .col(ColumnDef::new(TradeOrders::Status).string().not_null())
                    .col(ColumnDef::new(TradeOrders::CreatedAt).timestamp().not_null())
                    .col(ColumnDef::new(TradeOrders::UpdatedAt).timestamp().not_null())
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_trade_orders_user_id")
                            .from(TradeOrders::Table, TradeOrders::UserId)
                            .to(Users::Table, Users::Id)
                            .on_delete(ForeignKeyAction::Cascade),
                    )
                    .index(
                        Index::create()
                            .name("idx_trade_orders_user_id_status")
                            .col(TradeOrders::UserId)
                            .col(TradeOrders::Status),
                    )
                    .to_owned(),
            )
            .await
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(TradeOrders::Table).to_owned())
            .await
    }
}

#[derive(Iden)]
enum TradeOrders {
    Table,
    Id,
    UserId,
    Symbol,
    OrderType,
    Side,
    Quantity,
    Price,
    Status,
    CreatedAt,
    UpdatedAt,
}
```

## WebSocket Message Handling

### Message Type Definitions
```rust
/// Strongly typed WebSocket messages with validation
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(tag = "type", content = "data")]
pub enum WsMessage {
    /// Client connection acknowledgment
    Connect {
        client_id: String
    },

    /// Market data subscription request
    Subscribe {
        channel: String,
        symbols: Vec<String>
    },

    /// Market data unsubscription request
    Unsubscribe {
        channel: String,
        symbols: Vec<String>
    },

    /// Real-time market data update
    MarketData {
        symbol: String,
        price: Decimal,
        volume: Decimal,
        timestamp: i64,
    },

    /// Order status update
    OrderUpdate {
        order_id: String,
        status: OrderStatus,
        filled_quantity: Option<Decimal>,
        average_price: Option<Decimal>,
    },

    /// Position update
    PositionUpdate {
        symbol: String,
        quantity: Decimal,
        unrealized_pnl: Decimal,
        timestamp: i64,
    },

    /// Server error response
    Error {
        code: i32,
        message: String,
        request_id: Option<String>,
    },

    /// Heartbeat messages
    Ping,
    Pong,

    /// Connection close notification
    Close {
        reason: String
    },

    /// Custom service messages
    Custom {
        event: String,
        data: serde_json::Value
    },
}

/// Message validation
impl WsMessage {
    pub fn validate(&self) -> Result<(), ValidationError> {
        match self {
            WsMessage::Subscribe { symbols, .. } => {
                if symbols.is_empty() {
                    return Err(ValidationError::EmptySymbolList);
                }
                for symbol in symbols {
                    if !is_valid_symbol(symbol) {
                        return Err(ValidationError::InvalidSymbol(symbol.clone()));
                    }
                }
            }
            WsMessage::MarketData { price, volume, .. } => {
                if *price <= Decimal::ZERO {
                    return Err(ValidationError::InvalidPrice(*price));
                }
                if *volume < Decimal::ZERO {
                    return Err(ValidationError::InvalidVolume(*volume));
                }
            }
            _ => {}
        }
        Ok(())
    }
}
```

### Message Handler Patterns
```rust
/// Centralized message routing with proper error handling
pub async fn process_websocket_message(
    client_id: &str,
    message: WsMessage,
    tx: &UnboundedSender<Message>,
    app_state: &AppState,
) -> Result<(), HyperTraderError> {
    // Validate message
    message.validate()
        .map_err(|e| HyperTraderError::WebSocketError {
            client_id: client_id.to_string(),
            reason: format!("Message validation failed: {}", e),
        })?;

    // Route to appropriate handler
    match message {
        WsMessage::Subscribe { channel, symbols } => {
            handle_subscription(client_id, &channel, &symbols, tx, app_state).await
        }

        WsMessage::Unsubscribe { channel, symbols } => {
            handle_unsubscription(client_id, &channel, &symbols, tx, app_state).await
        }

        WsMessage::Custom { event, data } => {
            handle_custom_event(client_id, &event, data, tx, app_state).await
        }

        WsMessage::Ping => {
            send_pong(client_id, tx).await
        }

        _ => {
            tracing::warn!("Unhandled message type from client {}: {:?}", client_id, message);
            Ok(())
        }
    }
}

/// Subscription handler with proper validation
async fn handle_subscription(
    client_id: &str,
    channel: &str,
    symbols: &[String],
    tx: &UnboundedSender<Message>,
    app_state: &AppState,
) -> Result<(), HyperTraderError> {
    // Validate channel
    if !is_valid_channel(channel) {
        return send_error(
            tx,
            400,
            format!("Invalid channel: {}", channel),
            None
        ).await;
    }

    // Register subscriptions
    for symbol in symbols {
        app_state.subscription_manager
            .subscribe(client_id, channel, symbol)
            .await
            .map_err(|e| HyperTraderError::WebSocketError {
                client_id: client_id.to_string(),
                reason: format!("Failed to subscribe to {}/{}: {}", channel, symbol, e),
            })?;
    }

    // Send confirmation
    let response = WsMessage::Custom {
        event: "subscription_confirmed".to_string(),
        data: serde_json::json!({
            "channel": channel,
            "symbols": symbols,
            "status": "subscribed"
        }),
    };

    send_message(tx, response).await
}
```

## API Endpoint Design

### Current Issues (Rating: 4/10)
** Problems in Current Codebase:**
```rust
// BAD: Inconsistent endpoint naming
Router::with_path("get_all_orders").goal(get_all_orders)
Router::with_path("cs/get_all_users").get(get_all_users)
Router::with_path("cs/post_create_limit_order").post(post_create_limit_order)

// BAD: No input validation
#[handler]
async fn post_create_limit_order(req: &mut Request, res: &mut Response) -> Result<(), GlobalError> {
    // No validation of request body
    let order_data = req.parse_body::<serde_json::Value>().await?;
    // Direct processing without validation
}
```

** Required API Design Standards:**
```rust
/// Consistent RESTful endpoint design
pub fn configure_api_routes() -> Router {
    Router::with_path("/apis/v1")
        .push(
            Router::with_path("/orders")
                .get(get_orders)           // GET /apis/v1/orders
                .post(create_order)        // POST /apis/v1/orders
                .push(
                    Router::with_path("/<order_id>")
                        .get(get_order)    // GET /apis/v1/orders/{id}
                        .put(update_order) // PUT /apis/v1/orders/{id}
                        .delete(cancel_order) // DELETE /apis/v1/orders/{id}
                )
        )
        .push(
            Router::with_path("/positions")
                .get(get_positions)        // GET /apis/v1/positions
                .push(
                    Router::with_path("/<symbol>")
                        .get(get_position) // GET /apis/v1/positions/{symbol}
                )
        )
        .push(
            Router::with_path("/market")
                .push(
                    Router::with_path("/prices")
                        .get(get_market_prices) // GET /apis/v1/market/prices
                )
                .push(
                    Router::with_path("/symbols")
                        .get(get_symbols)   // GET /apis/v1/market/symbols
                )
        )
}

/// Request/Response models with validation
#[derive(Debug, Deserialize, Validate)]
pub struct CreateOrderRequest {
    #[validate(length(min = 1, max = 20))]
    pub symbol: String,

    #[validate(custom = "validate_order_side")]
    pub side: OrderSide,

    #[validate(range(min = 0.000001))]
    pub quantity: Decimal,

    #[validate(range(min = 0.01))]
    pub price: Option<Decimal>,

    pub order_type: OrderType,

    #[validate(range(min = 1, max = 100))]
    pub leverage: Option<u8>,
}

#[derive(Debug, Serialize)]
pub struct OrderResponse {
    pub id: String,
    pub symbol: String,
    pub side: OrderSide,
    pub quantity: Decimal,
    pub price: Option<Decimal>,
    pub status: OrderStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Standardized API response format
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<ApiError>,
    pub meta: Option<ResponseMeta>,
}

#[derive(Debug, Serialize)]
pub struct ResponseMeta {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub total: Option<u64>,
    pub total_pages: Option<u32>,
}

/// Proper handler implementation with validation
#[handler]
pub async fn create_order(
    req: &mut Request,
    res: &mut Response,
) -> Result<(), GlobalError> {
    // Extract and validate request
    let order_request = req.parse_body::<CreateOrderRequest>()
        .await
        .map_err(|e| GlobalError::bad_request(format!("Invalid request body: {}", e)))?;

    // Validate using validator crate
    order_request.validate()
        .map_err(|e| GlobalError::bad_request(format!("Validation failed: {}", e)))?;

    // Extract user context
    let user_id = extract_user_id(req)?;

    // Get app state
    let app_state = req.extensions()
        .get::<AppState>()
        .ok_or_else(|| GlobalError::internal_error("App state not found"))?;

    // Process order
    let order = app_state.order_manager
        .create_order(user_id, order_request)
        .await
        .map_err(GlobalError::from)?;

    // Return response
    let response = ApiResponse {
        success: true,
        data: Some(OrderResponse::from(order)),
        error: None,
        meta: None,
    };

    res.status_code(StatusCode::CREATED);
    res.render(Json(response));

    Ok(())
}

/// Query parameter handling
#[derive(Debug, Deserialize, Validate)]
pub struct GetOrdersQuery {
    #[validate(range(min = 1, max = 100))]
    pub limit: Option<u32>,

    #[validate(range(min = 0))]
    pub offset: Option<u32>,

    pub symbol: Option<String>,
    pub status: Option<OrderStatus>,

    #[serde(with = "chrono::serde::ts_seconds_option")]
    pub from_date: Option<DateTime<Utc>>,

    #[serde(with = "chrono::serde::ts_seconds_option")]
    pub to_date: Option<DateTime<Utc>>,
}

#[handler]
pub async fn get_orders(
    req: &mut Request,
    res: &mut Response,
) -> Result<(), GlobalError> {
    // Parse query parameters
    let query = req.parse_queries::<GetOrdersQuery>()
        .map_err(|e| GlobalError::bad_request(format!("Invalid query parameters: {}", e)))?;

    // Validate query
    query.validate()
        .map_err(|e| GlobalError::bad_request(format!("Query validation failed: {}", e)))?;

    let user_id = extract_user_id(req)?;
    let app_state = req.extensions().get::<AppState>()
        .ok_or_else(|| GlobalError::internal_error("App state not found"))?;

    // Build filters and pagination
    let filters = OrderFilters {
        symbol: query.symbol,
        status: query.status,
        from_date: query.from_date,
        to_date: query.to_date,
    };

    let pagination = Pagination {
        limit: query.limit.unwrap_or(20),
        offset: query.offset.unwrap_or(0),
    };

    // Fetch orders
    let (orders, total) = app_state.order_manager
        .get_user_orders(user_id, filters, pagination)
        .await
        .map_err(GlobalError::from)?;

    // Calculate pagination meta
    let total_pages = (total as f64 / pagination.limit as f64).ceil() as u32;

    let response = ApiResponse {
        success: true,
        data: Some(orders.into_iter().map(OrderResponse::from).collect::<Vec<_>>()),
        error: None,
        meta: Some(ResponseMeta {
            page: Some(pagination.offset / pagination.limit + 1),
            per_page: Some(pagination.limit),
            total: Some(total),
            total_pages: Some(total_pages),
        }),
    };

    res.status_code(StatusCode::OK);
    res.render(Json(response));

    Ok(())
}
```

## Security Practices

### Input Validation and Sanitization
```rust
/// Comprehensive input validation
use validator::{Validate, ValidationError};

#[derive(Debug, Deserialize, Validate)]
pub struct UserRegistrationRequest {
    #[validate(email)]
    pub email: String,

    #[validate(length(min = 8, max = 128))]
    #[validate(custom = "validate_password_strength")]
    pub password: String,

    #[validate(length(min = 2, max = 50))]
    #[validate(regex = "VALID_NAME_REGEX")]
    pub name: String,
}

fn validate_password_strength(password: &str) -> Result<(), ValidationError> {
    let has_uppercase = password.chars().any(|c| c.is_uppercase());
    let has_lowercase = password.chars().any(|c| c.is_lowercase());
    let has_digit = password.chars().any(|c| c.is_digit(10));
    let has_special = password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c));

    if has_uppercase && has_lowercase && has_digit && has_special {
        Ok(())
    } else {
        Err(ValidationError::new("password_too_weak"))
    }
}

/// SQL injection prevention with SeaORM
pub async fn get_user_by_email_safe(
    email: &str,
    db: &DatabaseConnection,
) -> Result<Option<user::Model>, DbErr> {
    // SeaORM automatically prevents SQL injection
    user::Entity::find()
        .filter(user::Column::Email.eq(email)) // Parameterized query
        .one(db)
        .await
}

/// Rate limiting middleware
#[derive(Clone)]
pub struct RateLimitMiddleware {
    store: Arc<DashMap<String, RateLimitState>>,
    max_requests: u32,
    window_seconds: u64,
}

impl RateLimitMiddleware {
    pub fn new(max_requests: u32, window_seconds: u64) -> Self {
        Self {
            store: Arc::new(DashMap::new()),
            max_requests,
            window_seconds,
        }
    }

    pub async fn check_rate_limit(&self, client_ip: &str) -> Result<(), GlobalError> {
        let now = Utc::now().timestamp() as u64;
        let window_start = now - self.window_seconds;

        let mut state = self.store.entry(client_ip.to_string())
            .or_insert_with(|| RateLimitState {
                requests: Vec::new(),
            });

        // Remove old requests
        state.requests.retain(|&timestamp| timestamp > window_start);

        // Check if limit exceeded
        if state.requests.len() >= self.max_requests as usize {
            return Err(GlobalError::rate_limited(
                "Too many requests. Please try again later."
            ));
        }

        // Add current request
        state.requests.push(now);

        Ok(())
    }
}
```

### Authentication and Authorization
```rust
/// JWT token handling
use jsonwebtoken::{decode, encode, DecodingKey, EncodingKey, Header, Validation};

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,  // user_id
    pub exp: usize,   // expiration
    pub iat: usize,   // issued at
    pub role: UserRole,
    pub permissions: Vec<String>,
}

pub struct AuthService {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
}

impl AuthService {
    pub fn new(secret: &[u8]) -> Self {
        Self {
            encoding_key: EncodingKey::from_secret(secret),
            decoding_key: DecodingKey::from_secret(secret),
        }
    }

    pub fn generate_token(&self, user: &user::Model) -> Result<String, GlobalError> {
        let expiration = Utc::now()
            .checked_add_signed(chrono::Duration::hours(24))
            .ok_or_else(|| GlobalError::internal_error("Failed to calculate expiration"))?
            .timestamp() as usize;

        let claims = Claims {
            sub: user.id.to_string(),
            exp: expiration,
            iat: Utc::now().timestamp() as usize,
            role: user.role,
            permissions: get_user_permissions(&user.role),
        };

        encode(&Header::default(), &claims, &self.encoding_key)
            .map_err(|e| GlobalError::internal_error(format!("Token generation failed: {}", e)))
    }

    pub fn validate_token(&self, token: &str) -> Result<Claims, GlobalError> {
        decode::<Claims>(token, &self.decoding_key, &Validation::default())
            .map(|data| data.claims)
            .map_err(|e| GlobalError::unauthorized(format!("Invalid token: {}", e)))
    }
}

/// Authorization middleware
#[handler]
pub async fn require_permission(
    req: &mut Request,
    res: &mut Response,
    ctrl: &mut FlowCtrl,
) -> Result<(), GlobalError> {
    let required_permission = req.extensions()
        .get::<RequiredPermission>()
        .ok_or_else(|| GlobalError::internal_error("Permission not configured"))?;

    let claims = req.extensions()
        .get::<Claims>()
        .ok_or_else(|| GlobalError::unauthorized("Authentication required"))?;

    if !claims.permissions.contains(&required_permission.0) {
        return Err(GlobalError::forbidden("Insufficient permissions"));
    }

    ctrl.call_next(req, res).await
}
```

## Testing Patterns

### Unit Testing
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use sea_orm::{Database, DatabaseBackend, MockDatabase, MockExecResult};

    /// Test database operations with mocked database
    #[tokio::test]
    async fn test_create_order_success() {
        // Setup mock database
        let db = MockDatabase::new(DatabaseBackend::Postgres)
            .append_query_results(vec![
                vec![order::Model {
                    id: 1,
                    user_id: 123,
                    symbol: "BTC".to_string(),
                    order_type: OrderType::Limit,
                    side: OrderSide::Buy,
                    quantity: Decimal::from_str("0.1").unwrap(),
                    price: Some(Decimal::from_str("45000.0").unwrap()),
                    status: OrderStatus::Pending,
                    created_at: Utc::now(),
                    updated_at: Utc::now(),
                }]
            ])
            .into_connection();

        let order_manager = OrderManager::new(db);

        let request = CreateOrderRequest {
            symbol: "BTC".to_string(),
            side: OrderSide::Buy,
            quantity: Decimal::from_str("0.1").unwrap(),
            price: Some(Decimal::from_str("45000.0").unwrap()),
            order_type: OrderType::Limit,
            leverage: None,
        };

        let result = order_manager.create_order(request).await;

        assert!(result.is_ok());
        let order = result.unwrap();
        assert_eq!(order.symbol, "BTC");
        assert_eq!(order.status, OrderStatus::Pending);
    }

    /// Test error handling
    #[tokio::test]
    async fn test_create_order_validation_error() {
        let db = MockDatabase::new(DatabaseBackend::Postgres).into_connection();
        let order_manager = OrderManager::new(db);

        let invalid_request = CreateOrderRequest {
            symbol: "".to_string(), // Invalid empty symbol
            side: OrderSide::Buy,
            quantity: Decimal::ZERO, // Invalid zero quantity
            price: Some(Decimal::from_str("45000.0").unwrap()),
            order_type: OrderType::Limit,
            leverage: None,
        };

        let result = order_manager.create_order(invalid_request).await;

        assert!(result.is_err());
        match result.unwrap_err() {
            HyperTraderError::InvalidMarketData { .. } => {
                // Expected error type
            }
            _ => panic!("Expected InvalidMarketData error"),
        }
    }
}
```

### Integration Testing
```rust
#[cfg(test)]
mod integration_tests {
    use super::*;
    use salvo::test::{ResponseExt, TestClient};

    /// Test complete API endpoint
    #[tokio::test]
    async fn test_create_order_endpoint() {
        let app = create_test_app().await;
        let client = TestClient::get(app);

        let order_request = CreateOrderRequest {
            symbol: "BTC".to_string(),
            side: OrderSide::Buy,
            quantity: Decimal::from_str("0.1").unwrap(),
            price: Some(Decimal::from_str("45000.0").unwrap()),
            order_type: OrderType::Limit,
            leverage: None,
        };

        let response = client
            .post("/apis/v1/orders")
            .add_header("Authorization", "Bearer valid_token", true)
            .json(&order_request)
            .send()
            .await;

        assert_eq!(response.status_code(), Some(StatusCode::CREATED));

        let body: ApiResponse<OrderResponse> = response.json().await;
        assert!(body.success);
        assert!(body.data.is_some());

        let order = body.data.unwrap();
        assert_eq!(order.symbol, "BTC");
        assert_eq!(order.status, OrderStatus::Pending);
    }
}
```

### WebSocket Testing
```rust
#[cfg(test)]
mod ws_tests {
    use super::*;
    use tokio_tungstenite::{connect_async, tungstenite::Message};

    #[tokio::test]
    async fn test_websocket_greeting() {
        let server = start_test_server().await;
        let ws_url = format!("ws://localhost:{}/ws", server.port());

        let (ws_stream, _) = connect_async(&ws_url).await.unwrap();
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();

        // Send greeting request
        let greeting_msg = WsMessage::Custom {
            event: "greeting_request".to_string(),
            data: serde_json::json!({
                "name": "Test User"
            }),
        };

        let msg_text = serde_json::to_string(&greeting_msg).unwrap();
        ws_sender.send(Message::Text(msg_text)).await.unwrap();

        // Receive response
        let response = ws_receiver.next().await.unwrap().unwrap();
        let response_text = response.into_text().unwrap();
        let response_msg: WsMessage = serde_json::from_str(&response_text).unwrap();

        match response_msg {
            WsMessage::Custom { event, data } => {
                assert_eq!(event, "greeting_response");
                assert!(data.get("greeting").is_some());
            }
            _ => panic!("Expected greeting response"),
        }
    }
}
```

## Code Quality Standards

### Current Issues Summary
Based on our analysis, here are the critical issues to address:

**IMMEDIATE FIXES REQUIRED:**

1. **Remove Dead Code (Priority: CRITICAL)**
```rust
// REMOVE THESE BLOCKS FROM router.rs:
if false {
    // ... 100+ lines of commented code
    std::process::exit(0);
}
```

2. **Add Documentation (Priority: HIGH)**
```rust
// CURRENT: No documentation
#[handler]
async fn get_all_orders(req: &mut Request, res: &mut Response) -> Result<(),GlobalError>

// REQUIRED: Full documentation
/// Retrieves all active trading orders for the current user.
///
/// Returns a paginated list of orders with filtering options.
#[handler]
pub async fn get_all_orders(req: &mut Request, res: &mut Response) -> Result<(), GlobalError>
```

3. **Standardize Language (Priority: MEDIUM)**
```rust
// CURRENT: Mixed languages
// 获取所有订单 - Chinese comment
// 定义持仓数据结构体 - Chinese comment

// REQUIRED: English only
/// Get all orders for the current user
/// Define position data structure
```

### Quality Checklist

**Before submitting any code, ensure:**

- [ ] **No dead code or commented-out blocks**
- [ ] **All public functions have documentation**
- [ ] **All error cases are handled with proper error types**
- [ ] **Input validation is implemented**
- [ ] **Tests are written and passing**
- [ ] **English-only comments and documentation**
- [ ] **Consistent naming conventions**
- [ ] **No unwrap() calls in production code**
- [ ] **Proper async/await usage**
- [ ] **Security considerations addressed**

### Code Review Standards

**Required for all pull requests:**

1. **Documentation Coverage**: Minimum 80% of public APIs documented
2. **Test Coverage**: Minimum 70% line coverage for new code
3. **Performance**: No blocking operations in async contexts
4. **Security**: All inputs validated, no SQL injection risks
5. **Error Handling**: All errors properly typed and handled
6. **Code Cleanliness**: No TODO comments, no dead code

## Environment Management

### Environment Variable Patterns
```rust
/// Centralized environment configuration
use std::env;
use anyhow::{Context, Result};

#[derive(Debug, Clone)]
pub struct Config {
    pub database_url: String,
    pub clickhouse_url: String,
    pub server_port: u16,
    pub jwt_secret: String,
    pub stage: Environment,
    pub log_level: String,
}

#[derive(Debug, Clone, PartialEq)]
pub enum Environment {
    Development,
    Staging,
    Production,
}

impl Config {
    pub fn from_env() -> Result<Self> {
        Ok(Self {
            database_url: env::var("DATABASE_URL")
                .context("DATABASE_URL must be set")?,
            clickhouse_url: env::var("CLICKHOUSE_URL")
                .context("CLICKHOUSE_URL must be set")?,
            server_port: env::var("SIMU_TRACING_API_LISTEN_PORT")
                .unwrap_or_else(|_| "9999".to_string())
                .parse()
                .context("Invalid port number")?,
            jwt_secret: env::var("JWT_SECRET")
                .context("JWT_SECRET must be set")?,
            stage: env::var("STAGE")
                .unwrap_or_else(|_| "development".to_string())
                .parse()?,
            log_level: env::var("RUST_LOG")
                .unwrap_or_else(|_| "info".to_string()),
        })
    }

    pub fn is_production(&self) -> bool {
        self.stage == Environment::Production
    }
}

impl std::str::FromStr for Environment {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self> {
        match s.to_lowercase().as_str() {
            "development" | "dev" => Ok(Environment::Development),
            "staging" | "stage" => Ok(Environment::Staging),
            "production" | "prod" => Ok(Environment::Production),
            _ => Err(anyhow::anyhow!("Invalid environment: {}", s)),
        }
    }
}
```

## Performance Guidelines

### Database Optimization
```rust
/// Use connection pooling
pub async fn create_database_connection() -> Result<DatabaseConnection> {
    let database_url = env::var("DATABASE_URL")?;

    Database::connect(&database_url)
        .await
        .context("Failed to connect to database")
}

/// Batch operations for better performance
pub async fn batch_insert_orders(
    orders: Vec<order::ActiveModel>,
    db: &DatabaseConnection,
) -> Result<Vec<order::Model>> {
    order::Entity::insert_many(orders)
        .exec_many_with_returning(db)
        .await
        .map_err(Into::into)
}

/// Use indexes for common queries
// In migration:
.index(
    Index::create()
        .name("idx_orders_user_status_created")
        .col(order::Column::UserId)
        .col(order::Column::Status)
        .col(order::Column::CreatedAt)
)
```

### Memory Management
```rust
/// Use Arc for shared state
pub struct AppState {
    pub db: Arc<DatabaseConnection>,
    pub ws_manager: Arc<WsManager>,
    pub config: Arc<Config>,
}

/// Avoid cloning large data structures
pub async fn process_large_dataset(
    data: &[MarketData], // Use reference instead of owned value
) -> Result<ProcessedData> {
    // Process without cloning
    Ok(ProcessedData::from_slice(data))
}
```

## Git Workflow

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(ws): add market data subscription handling

- Implement subscription management for real-time market data
- Add validation for symbol lists
- Include proper error handling for invalid channels

Closes #123
```

### Branch Naming
- `feature/description` - New features
- `fix/description` - Bug fixes
- `refactor/description` - Code refactoring
- `docs/description` - Documentation updates

### Pre-commit Checklist
- [ ] Code compiles without warnings
- [ ] All tests pass
- [ ] Documentation updated
- [ ] No dead code or TODO comments
- [ ] Proper error handling implemented
- [ ] Security considerations reviewed

---

## Summary

This document establishes comprehensive coding standards for the xbit-hypertrader project. Following these conventions will improve code quality from the current **6/10** rating to **professional standards (8-9/10)**.

**Immediate Action Items:**
1. Remove all dead code blocks from router.rs
2. Add comprehensive documentation to all public APIs
3. Standardize on English-only comments
4. Implement proper input validation
5. Add comprehensive test coverage

**Long-term Goals:**
- Achieve 80%+ documentation coverage
- Implement 70%+ test coverage
- Establish automated code quality checks
- Create comprehensive API documentation
- Implement security best practices

By following these standards, the codebase will become more maintainable, secure, and professional.