#!/bin/bash
# filepath: /Users/<USER>/Github/xbit-dex/xbit-hypertrader/start.sh

# Start the main hypertrader application
echo "Starting main hypertrader application..."
./hypertrader &
MAIN_PID=$!

# Start the range updater in the background
echo "Starting range_updater service..."
./range_updater &
RANGE_PID=$!

# Function to handle shutdown
handle_shutdown() {
    echo "Shutting down services..."

    # Kill all processes
    kill $MAIN_PID $RANGE_PID 2>/dev/null

    exit 0
}

# Set trap for graceful shutdown
trap handle_shutdown SIGTERM SIGINT

# Keep the script running
wait -n