use super::interval::KlineInterval;

/// K线数据范围信息
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct KlineRange {
    pub symbol: String,
    pub interval: KlineInterval,
    pub start_time: i64,
    pub end_time: i64,
    pub count: u64,
}

impl KlineRange {
    pub fn new(
        symbol: String,
        interval: KlineInterval,
        start_time: i64,
        end_time: i64,
        count: u64,
    ) -> Self {
        Self {
            symbol,
            interval,
            start_time,
            end_time,
            count,
        }
    }

    pub fn verified(&self) -> i64 {
        let interval_in_seconds = self.interval.to_miliseconds();
        if interval_in_seconds == 0 {
            return 0;
        }

        let time_diff = self.end_time - self.start_time;
        time_diff / interval_in_seconds as i64 - self.count as i64
    }

    pub fn to_string_with_verified(&self) -> String {
        let self_value = serde_json::json!({
            "symbol": self.symbol,
            "interval": self.interval,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "count": self.count,
            "verified": self.verified(),
        });

        serde_json::to_string_pretty(&self_value).unwrap_or_default()
    }
}
