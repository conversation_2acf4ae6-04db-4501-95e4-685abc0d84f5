use super::*;
use strum::{Enum<PERSON><PERSON>, IntoEnumIterator};

// 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 8h, 12h, 1d, 3d, 1w, 1M
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize, EnumIter)]
pub enum KlineInterval {
    OneMinute,
    ThreeMinutes,
    FiveMinutes,
    FifteenMinutes,
    ThirtyMinutes,
    OneHour,
    TwoHours,
    FourHours,
    EightHours,
    TwelveHours,
    OneDay,
    ThreeDays,
    OneWeek,
    OneMonth,
}

impl std::str::FromStr for KlineInterval {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "1m" => KlineInterval::OneMinute,
            "3m" => KlineInterval::ThreeMinutes,
            "5m" => KlineInterval::FiveMinutes,
            "15m" => KlineInterval::FifteenMinutes,
            "30m" => KlineInterval::ThirtyMin<PERSON>,
            "1h" => KlineInterval::OneHour,
            "2h" => KlineInterval::TwoHours,
            "4h" => KlineInterval::FourHours,
            "8h" => KlineInterval::EightHours,
            "12h" => KlineInterval::TwelveHours,
            "1d" => KlineInterval::OneDay,
            "3d" => KlineInterval::ThreeDays,
            "1w" => KlineInterval::OneWeek,
            "1M" => KlineInterval::OneMonth,
            _ => return Err(anyhow::anyhow!("Invalid kline interval: {}", s)),
        })
    }
}

impl ToString for KlineInterval {
    fn to_string(&self) -> String {
        match self {
            KlineInterval::OneMinute => "1m".to_string(),
            KlineInterval::ThreeMinutes => "3m".to_string(),
            KlineInterval::FiveMinutes => "5m".to_string(),
            KlineInterval::FifteenMinutes => "15m".to_string(),
            KlineInterval::ThirtyMinutes => "30m".to_string(),
            KlineInterval::OneHour => "1h".to_string(),
            KlineInterval::TwoHours => "2h".to_string(),
            KlineInterval::FourHours => "4h".to_string(),
            KlineInterval::EightHours => "8h".to_string(),
            KlineInterval::TwelveHours => "12h".to_string(),
            KlineInterval::OneDay => "1d".to_string(),
            KlineInterval::ThreeDays => "3d".to_string(),
            KlineInterval::OneWeek => "1w".to_string(),
            KlineInterval::OneMonth => "1M".to_string(),
        }
    }
}

impl KlineInterval {
    pub fn to_miliseconds(&self) -> u64 {
        match self {
            KlineInterval::OneMinute => 60000,
            KlineInterval::ThreeMinutes => 180000,
            KlineInterval::FiveMinutes => 300000,
            KlineInterval::FifteenMinutes => 900000,
            KlineInterval::ThirtyMinutes => 1800000,
            KlineInterval::OneHour => 3600000,
            KlineInterval::TwoHours => 7200000,
            KlineInterval::FourHours => 14400000,
            KlineInterval::EightHours => 28800000,
            KlineInterval::TwelveHours => 43200000,
            KlineInterval::OneDay => 86400000,
            KlineInterval::ThreeDays => 259200000,
            KlineInterval::OneWeek => 604800000,
            KlineInterval::OneMonth => 2592000000,
        }
    }

    /// 获取所有可能的K线间隔
    pub fn all() -> Vec<KlineInterval> {
        KlineInterval::iter().collect()
    }

    /// 获取所有可能的K线间隔的字符串表示
    pub fn all_strings() -> Vec<String> {
        KlineInterval::iter()
            .map(|interval| interval.to_string())
            .collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_kline_interval_all() {
        let all_intervals = KlineInterval::all();
        assert_eq!(all_intervals.len(), 14);

        let all_strings = KlineInterval::all_strings();
        assert_eq!(all_strings.len(), 14);
        assert!(all_strings.contains(&"1m".to_string()));
        assert!(all_strings.contains(&"1d".to_string()));
    }
}
