CREATE TABLE prices (
    token_symbol VARCHAR(50) NOT NULL,
    price DOUBLE PRECISION NOT NULL,
    timestamp BIGINT NOT NULL,
    PRIMARY KEY (token_symbol, timestamp)
);

-- 添加索引以提高查询性能
CREATE INDEX idx_timestamp ON prices(timestamp);

-- 添加注释（PostgreSQL中分别为字段添加注释）
COMMENT ON COLUMN prices.token_symbol IS '币种符号';
COMMENT ON COLUMN prices.price IS '实时价格';
COMMENT ON COLUMN prices.timestamp IS '价格记录时间';