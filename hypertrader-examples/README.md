# HyperTrader Examples

## Usage

1. environment setup

.env config file

``` env
TEST_USER0_SECRET=aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
TEST_USER1_SECRET=bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb
TEST_USER2_SECRET=cccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccc
TEST_USER3_SECRET=dddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
```

note: replace with your own secrets, without 0x prefix

put this in a .env file, store in the root directory of the project or the parent directory of the project

2. run example

``` bash
cargo run --bin xxxxx
```

### Examples

#### Vault operations

- `vault_withdraw.rs` - Withdraw from vault
- `vault_deposit.rs` - Deposit to vault
- `vault_order.rs` - Order using vault as leader

#### Multisig operations

- `multisig_usd_send.rs` - Send USD from multisig account
- `multisig_order.rs` - Order using multisig account


#### Multisig Vault operations

- `multisig_vault_withdraw.rs` - Withdraw from vault whose leader is multisig account
- `multisig_vault_deposit.rs` - Deposit to vault from a multisig account
- `multisig_vault_order.rs` - Order using vault whose leader is multisig account

