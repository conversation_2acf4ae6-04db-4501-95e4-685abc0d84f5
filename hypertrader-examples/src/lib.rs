pub const TEST_USER0: &str = "0x2b2fbbe44c52ab8e6fef1d4748c78388af171307"; // multi-sig address 1. vault in mainnet
pub const TEST_USER1: &str = "0x587da46a2a234304fbd05d0276f288d44de75a06"; // main vault address, (multi-sig user in testnet)
pub const TEST_USER2: &str = "0x2f8f867b5e7c952e2e61e1e04fb55e631fac048d"; // multi-sig address 2
pub const TEST_USER3: &str = "0x8c15fbfbb08f23e8cdd889f1f13e8c68407dcc5e"; // multi-sig address 3, destination address

pub const SINGLE_ACCOUNT_VAULT_ADDRESS: &str = "0x586d7716b4c417d353e945b310e4cee90630e564"; // vault created by TEST_USER3 on testnet
pub const MULTI_SIG_VAULT_ADDRESS: &str = "0x976d51e00836042c63b37f6ef898f954ec6149ed"; // controlled by multi-sig address TEST_USER1 on testnet

pub const TEST_USER5: &str = "0x9745844956b937214a3A00cEf8f87d03249f0a12";
