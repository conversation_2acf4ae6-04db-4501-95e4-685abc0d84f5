use hyperliquid_rust_sdk::{Address, next_nonce};
use hypertrader_examples::*;
use hypertrader_hyperliquid::client::{data::DataClient, single_account::SingleAccountClient};
use hypertrader_utils::init::ensure_inited;
use std::str::FromStr;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER3_SECRET").unwrap();
    let user = Address::from_str(TEST_USER3).unwrap();

    let (data_client, _) = DataClient::new(is_testnet).await?;
    let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let vaults = &data_client.user_vault_equities_with_details(user).await?;
    // tracing::info!("vaults: {vaults:#?}");
    let leaders = vaults
        .iter()
        .map(|v| v.vault_address.clone())
        .collect::<Vec<_>>();
    tracing::info!("user: {user}, owns vaults: {leaders:#?}");

    let nonce = next_nonce();
    let res = client
        .withdraw_usd_from_vault(3000000, SINGLE_ACCOUNT_VAULT_ADDRESS.to_string(), nonce)
        .await?;

    tracing::info!("res: {res:?}");

    Ok(())
}
