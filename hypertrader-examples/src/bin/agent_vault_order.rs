use hyperliquid_rust_sdk::ClientLimit;
use hyperliquid_rust_sdk::ClientOrder;
use hyperliquid_rust_sdk::ClientOrderRequest;
use hyperliquid_rust_sdk::next_nonce;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    // agent private key
    // let secret_key = "4671f59f2800c6bc00c427017618e52ff6f5b3434b41640ab6b745f849654430";
    let secret_key = "ce8ba13ea2b210c30e4e940cf47c45bcd096d6e1f38badd455df592166dd421f";

    // HL:0x8ad5d36232016f9385c273b008b6dc40af54bb78
    let agent_vault_address = "0x8ad5d36232016f9385c273b008b6dc40af54bb78";

    // let client = SingleAccountClient::new(&secret_key, is_testnet).await;
    let client = SingleAccountClient::new_with_vault(
        &secret_key,
        agent_vault_address.parse().unwrap(),
        is_testnet,
    )
    .await;
    println!("Current agent account: {}", client.address());

    // order:
    let order = ClientOrderRequest {
        asset: "ORDI".to_string(),
        is_buy: true,
        reduce_only: false,
        limit_px: 7.8,
        sz: 4.5,
        cloid: None,
        order_type: ClientOrder::Limit(ClientLimit {
            tif: "Gtc".to_string(),
        }),
    };

    let res = client.vault_order(order, next_nonce()).await;
    println!("Order response: {res:?}");

    Ok(())
}
