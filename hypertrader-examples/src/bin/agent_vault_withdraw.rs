use hyperliquid_rust_sdk::next_nonce;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

// 提款会直接提到原账户
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = "4671f59f2800c6bc00c427017618e52ff6f5b3434b41640ab6b745f849654430";
    // let secret_key = "ce8ba13ea2b210c30e4e940cf47c45bcd096d6e1f38badd455df592166dd421f";

    // HL:0x8ad5d36232016f9385c273b008b6dc40af54bb78
    let agent_vault_address = "0x8ad5d36232016f9385c273b008b6dc40af54bb78";

    let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let nonce = next_nonce();
    let res = client
        .withdraw_usd_from_vault(3000000, agent_vault_address.to_string(), nonce)
        .await?;

    tracing::info!("res: {res:?}");

    Ok(())
}
