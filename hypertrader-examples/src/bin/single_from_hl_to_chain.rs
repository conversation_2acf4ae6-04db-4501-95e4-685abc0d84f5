use hypertrader_examples::TEST_USER2;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER2_SECRET").unwrap();

    let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let res = client
        .client
        .withdraw_from_bridge("6.6".to_string(), TEST_USER2.to_string(), None)
        .await?;

    tracing::info!("res: {res:?}");

    Ok(())
}
