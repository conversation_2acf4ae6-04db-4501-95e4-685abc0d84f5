use hyperliquid_rust_sdk::{Address, LocalWallet, next_nonce};
use hypertrader_examples::*;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;
use std::str::FromStr;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER0_SECRET").unwrap();
    let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let multi_sig_user = TEST_USER1;

    let test_wallet0: LocalWallet = std::env::var("TEST_USER0_SECRET").unwrap().parse().unwrap();
    let test_wallet2: LocalWallet = std::env::var("TEST_USER2_SECRET").unwrap().parse().unwrap();
    let test_wallet3: LocalWallet = std::env::var("TEST_USER3_SECRET").unwrap().parse().unwrap();
    let authorized_wallets = vec![test_wallet2, test_wallet3, test_wallet0];

    let multi_sig_user = Address::from_str(multi_sig_user).unwrap();
    let out_signer = Address::from_str(TEST_USER0).unwrap();

    let nonce = next_nonce();

    // 收集签名
    let mut signatures = Vec::new();
    for wallet in authorized_wallets {
        let signature = client
            .client
            .multi_sig_vault_transfer_sig(
                MULTI_SIG_VAULT_ADDRESS.to_string(),
                false,
                3000000,
                Some(&wallet),
                multi_sig_user,
                out_signer,
                nonce,
            )
            .await?;

        signatures.push(signature);
        if signatures.len() == 2 {
            break;
        }
    }

    let res = client
        .client
        .multi_sig_vault_transfer(
            false,
            3000000,
            MULTI_SIG_VAULT_ADDRESS.to_string(),
            signatures,
            None,
            multi_sig_user,
            nonce,
        )
        .await;

    tracing::info!("res: {res:#?}");
    Ok(())
}
