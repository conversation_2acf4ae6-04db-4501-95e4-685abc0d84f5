use hyperliquid_rust_sdk::Address;
use hyperliquid_rust_sdk::ClientLimit;
use hyperliquid_rust_sdk::ClientOrder;
use hyperliquid_rust_sdk::ClientOrderRequest;
use hyperliquid_rust_sdk::LocalWallet;
use hyperliquid_rust_sdk::Signer;
use hyperliquid_rust_sdk::next_nonce;
use hypertrader_examples::TEST_USER0;
use hypertrader_examples::TEST_USER2;
use hypertrader_examples::TEST_USER5;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;
use std::str::FromStr;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;

    ////////////////// TEST_USER2 approve agent
    // let secret_key = std::env::var("TEST_USER2_SECRET").unwrap();
    // println!("Origianl account: {}", TEST_USER2);

    // let client = SingleAccountClient::new(&secret_key, is_testnet).await;
    // let (private_key, response) = client.client.approve_agent(None).await.unwrap();
    // println!("Agent creation response: {response:?}");
    // println!("\nAgent private_key: {:?}", private_key);

    // let wallet: LocalWallet = private_key.parse().unwrap();
    // println!("Agent wallet: {:#x}", wallet.address());

    ////////////////// Agent wallet to multisig
    // let client = SingleAccountClient::new(
    //     "1bcf27967755bd659ab18d4fa1722078cce2d91e122552079f11c48877562112",
    //     is_testnet,
    // )
    // .await;
    // let res = client
    //     .convert_to_multi_sig_account(vec![TEST_USER0.to_string(), TEST_USER5.to_string()], 1)
    //     .await?;
    // tracing::info!("res: {res:#?}");

    /////////////////////// agent wallet multisig order
    // let secret_key = std::env::var("TEST_USER0_SECRET").unwrap();
    // let client = SingleAccountClient::new(&secret_key, is_testnet).await;
    // let test_wallet0: LocalWallet = std::env::var("TEST_USER0_SECRET").unwrap().parse().unwrap();

    // // let multi_sig_user = Address::from_str("******************************************").unwrap();
    // let multi_sig_user = Address::from_str(TEST_USER2).unwrap();
    // let out_signer = Address::from_str(TEST_USER0).unwrap();
    // let nonce = next_nonce();
    // // order:
    // let order = ClientOrderRequest {
    //     asset: "ORDI".to_string(),
    //     is_buy: true,
    //     reduce_only: false,
    //     limit_px: 7.3,
    //     sz: 3.0,
    //     cloid: None,
    //     order_type: ClientOrder::Limit(ClientLimit {
    //         tif: "Gtc".to_string(),
    //     }),
    // };
    // // 收集签名
    // let signatures = vec![
    //     client
    //         .client
    //         .multi_sig_order_sig(
    //             vec![order.clone()],
    //             Some(&test_wallet0),
    //             multi_sig_user,
    //             out_signer,
    //             nonce,
    //         )
    //         .await?,
    // ];
    // let res = client
    //     .client
    //     .multi_sig_order(vec![order], signatures, None, multi_sig_user, nonce)
    //     .await;
    // tracing::info!("res: {res:#?}");

    // /////////////////////// order as agent wallet
    let secret_key = "1bcf27967755bd659ab18d4fa1722078cce2d91e122552079f11c48877562112";
    let client = SingleAccountClient::new(&secret_key, is_testnet).await;
    println!("Current agent account: {}", client.address());
    // order:
    // 只能下maker单。
    // 用limit(Gtc)下maker单的情况，bbo是7.278@7.2837. asset=56。 会报错无法执行
    // Error("Only post-only orders allowed immediately after network upgrade")]
    // 用post only(Alo)下立即成交单的情况，bbo是7.278@7.2837. asset=56。 会报错无法执行
    // Post only order would have immediately matched, bbo was 7.278@7.2837. asset=56
    let order = ClientOrderRequest {
        asset: "ORDI".to_string(),
        is_buy: true,
        reduce_only: false,
        limit_px: 7.31,
        sz: 3.0,
        cloid: None,
        order_type: ClientOrder::Limit(ClientLimit {
            tif: "Gtc".to_string(),
        }),
    };
    let res = client.order(order, None, next_nonce()).await;
    println!("Order response: {res:?}");

    Ok(())
}

// Agent private_key: "1bcf27967755bd659ab18d4fa1722078cce2d91e122552079f11c48877562112"
// Agent wallet: ******************************************
