use ethers::core::types::Address;
use ethers::utils::keccak256;
use hex;
use hyperliquid_rust_sdk::ClientLimit;
use hyperliquid_rust_sdk::ClientOrder;
use hyperliquid_rust_sdk::ClientOrderRequest;
use hyperliquid_rust_sdk::LocalWallet;
use hyperliquid_rust_sdk::next_nonce;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;
use secp256k1::{Message, Secp256k1, ecdsa::RecoverableSignature};
use tiny_keccak::{Hasher, Keccak};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    // agent private key
    let secret_key = "4671f59f2800c6bc00c427017618e52ff6f5b3434b41640ab6b745f849654430";
    let client = SingleAccountClient::new(&secret_key, is_testnet).await;
    println!("Current agent account: {}", client.address());

    // order:
    // 只能下maker单。

    // 用limit(Gtc)下maker单的情况，bbo是7.278@7.2837. asset=56。 会报错无法执行
    // Error("Only post-only orders allowed immediately after network upgrade")]
    // 用post only(Alo)下立即成交单的情况，bbo是7.278@7.2837. asset=56。 会报错无法执行
    // Post only order would have immediately matched, bbo was 7.278@7.2837. asset=56
    let order = ClientOrderRequest {
        asset: "ORDI".to_string(),
        is_buy: true,
        reduce_only: false,
        limit_px: 7.31,
        sz: 3.0,
        cloid: None,
        order_type: ClientOrder::Limit(ClientLimit {
            tif: "Gtc".to_string(),
        }),
    };

    let res = client.order(order, None, next_nonce()).await;
    println!("Order response: {res:?}");

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    use hyperliquid_rust_sdk::LocalWallet;
    use hyperliquid_rust_sdk::{H256, Signer, sign_hash};

    #[test]
    fn test_sign() {
        let private_key = "9f5376dbeb95d232d9214dda981c340b662f611ada7964aff6c6036982efc9aa";
        let wallet = LocalWallet::from_str(private_key).unwrap();
        let address = wallet.address();
        println!("address: {:x}", address);

        // wallet from public key
        let wallet = LocalWallet::from_str(private_key).unwrap();
        let address = wallet.address();
        println!("address: {:x}", address);
        assert_eq!(
            format!("{:x}", address),
            "a1abb3c8f76e1298776fdb4c3e8f8c9428aa1d74"
        );

        let message = "0x0e68ea5cf8ed8360a46aafbca3e166694bbab78e145c5dc39c6411d9c4354c99";
        let signed = sign_hash(H256::from_str(message).unwrap(), &wallet).unwrap();
        println!("signed: {}", signed.to_string());
        let signed_str = signed.to_string();

        // 验证签名
        let message_hash = H256::from_str(message).unwrap();
        println!("消息哈希: {:x}", message_hash);

        // 从签名中恢复公钥和地址
        let (recovered_address, is_valid) = verify_signature(
            message_hash.as_bytes(),
            &signed_str,
            &format!("{:x}", address),
        );

        println!("\n签名验证结果:");
        println!("预期地址: {:x}", address);
        println!("恢复地址: {}", recovered_address);
        println!("签名有效: {}", if is_valid { "是" } else { "否" });
    }

    #[test]
    fn test_sign_hash() {
        // 验证签名
        let message = "0x0e68ea5cf8ed8360a46aafbca3e166694bbab78e145c5dc39c6411d9c4354c99";
        let message_hash = H256::from_str(message).unwrap();
        println!("消息哈希: {:x}", message_hash);

        let signed_str = "3045022100ad67950bb23a74b851def625938c2f14bed4b1827edf813fe3d2aba65a20ebb10220045e39c5adf28919e491a78a7277441e34ff595f87c8f6bce36559bc3210f5dc";
        let signed_str = der_to_ethereum_signature(signed_str).unwrap();

        let expected_address = "a1abb3c8f76e1298776fdb4c3e8f8c9428aa1d74";

        let (recovered_address, is_valid) =
            verify_signature(message_hash.as_bytes(), &signed_str, &expected_address);
        println!("恢复地址: {}", recovered_address);
        println!("签名有效: {}", if is_valid { "是" } else { "否" });
    }

    #[test]
    fn test_der_to_ethereum_signature() {
        let der_str = "3045022100ad67950bb23a74b851def625938c2f14bed4b1827edf813fe3d2aba65a20ebb10220045e39c5adf28919e491a78a7277441e34ff595f87c8f6bce36559bc3210f5dc";
        let signed_str = der_to_ethereum_signature(der_str).unwrap();
        println!("signed_str: {}", signed_str);
        // ad67950bb23a74b851def625938c2f14bed4b1827edf813fe3d2aba65a20ebb1045e39c5adf28919e491a78a7277441e34ff595f87c8f6bce36559bc3210f5dc1b
    }

    #[test]
    fn test_address_from_pubkey() {
        // 以太坊地址生成步骤:
        // 1. 从公钥中去掉前缀字节(04)
        // 2. 对剩余的公钥字节进行Keccak-256哈希
        // 3. 取哈希的最后20字节作为地址

        let private_key = "9f5376dbeb95d232d9214dda981c340b662f611ada7964aff6c6036982efc9aa";
        let wallet = LocalWallet::from_str(private_key).unwrap();
        let expected_address = wallet.address();
        println!("通过私钥生成的地址: {:x}", expected_address);

        // 未压缩公钥
        let uncompressed_pubkey = "046e0be30f5f8b9a28b089151e555525066d1bf7f50c6553ea1136c5c5c134ef90f5c72d092a0d73957ccfb1f94fd8af7f02b3debb1bfa2ac992a8db7287666d12";
        let address = pubkey_to_eth_address(uncompressed_pubkey);
        println!("从未压缩公钥生成的地址: 0x{}", address);

        // 压缩公钥
        let compressed_pubkey =
            "026e0be30f5f8b9a28b089151e555525066d1bf7f50c6553ea1136c5c5c134ef90";
        let address_from_compressed = pubkey_to_eth_address(compressed_pubkey);
        println!("从压缩公钥生成的地址: 0x{}", address_from_compressed);

        // 验证地址
        let expected_addr_str = format!("{:x}", expected_address);
        println!("\n地址验证结果:");
        println!("从私钥生成的地址: 0x{}", expected_addr_str);
        println!("从未压缩公钥: 0x{}", address);
        println!("从压缩公钥: 0x{}", address_from_compressed);
        println!(
            "地址匹配: {}",
            if address == expected_addr_str {
                "是"
            } else {
                "否"
            }
        );
    }

    // 使用ethers库从公钥生成以太坊地址
    fn pubkey_to_eth_address(pubkey_hex: &str) -> String {
        // 移除0x前缀（如果有）
        let pubkey_hex = if pubkey_hex.starts_with("0x") {
            &pubkey_hex[2..]
        } else {
            pubkey_hex
        };

        // 解析十六进制公钥
        let pubkey_bytes = hex::decode(pubkey_hex).expect("无效的十六进制公钥");

        // 处理压缩和未压缩公钥格式
        let key_bytes = if pubkey_bytes[0] == 0x04 && pubkey_bytes.len() == 65 {
            // 未压缩公钥，去掉0x04前缀
            &pubkey_bytes[1..]
        } else if (pubkey_bytes[0] == 0x02 || pubkey_bytes[0] == 0x03) && pubkey_bytes.len() == 33 {
            // 使用secp256k1解析压缩公钥
            let secp = Secp256k1::new();

            match secp256k1::PublicKey::from_slice(&pubkey_bytes) {
                Ok(pubkey) => {
                    // 转换为未压缩格式
                    let uncompressed = pubkey.serialize_uncompressed();
                    // 去除0x04前缀
                    return pubkey_to_eth_address(&hex::encode(&uncompressed));
                }
                Err(e) => {
                    println!("错误: 无法解析压缩公钥 - {:?}", e);
                    &pubkey_bytes[1..] // 错误回退，可能结果不正确
                }
            }
        } else {
            // 不是标准格式，原样处理
            &pubkey_bytes
        };

        // 使用ethers库的keccak256函数
        let hash = keccak256(key_bytes);

        // 取哈希的最后20字节作为地址
        let eth_address = &hash[12..32];

        // 转换为十六进制字符串
        hex::encode(eth_address)
    }

    // 使用secp256k1库验证签名并恢复地址
    fn verify_signature(
        message_hash: &[u8],
        signature_hex: &str,
        expected_address: &str,
    ) -> (String, bool) {
        // 1. 解析签名
        let signature_bytes = match hex::decode(signature_hex) {
            Ok(bytes) => bytes,
            Err(e) => {
                println!("错误: 无法解析签名 - {}", e);
                return ("签名解析失败".to_string(), false);
            }
        };

        // 2. 验证签名格式
        if signature_bytes.len() != 65 {
            println!(
                "错误: 非法的签名长度 {} (预期65字节)",
                signature_bytes.len()
            );
            return ("签名长度错误".to_string(), false);
        }

        // 3. 提取签名部分
        let v = signature_bytes[64]; // 恢复ID
        let recovery_id = match secp256k1::ecdsa::RecoveryId::from_i32(if v >= 27 {
            (v - 27) as i32
        } else {
            v as i32
        }) {
            Ok(id) => id,
            Err(e) => {
                println!("错误: 无效的恢复ID - {:?}", e);
                return ("无效的恢复ID".to_string(), false);
            }
        };

        let r_s = &signature_bytes[0..64];
        let mut sig_data = [0u8; 64];
        sig_data.copy_from_slice(r_s);

        // 4. 创建可恢复签名
        let recoverable_sig = match RecoverableSignature::from_compact(&sig_data, recovery_id) {
            Ok(sig) => sig,
            Err(e) => {
                println!("错误: 无法创建可恢复签名 - {:?}", e);
                return ("无法创建可恢复签名".to_string(), false);
            }
        };

        // 5. 创建上下文和消息对象
        let secp = Secp256k1::new();
        let message = match Message::from_digest_slice(message_hash) {
            Ok(msg) => msg,
            Err(e) => {
                println!("错误: 无效的消息哈希 - {:?}", e);
                return ("无效的消息哈希".to_string(), false);
            }
        };

        // 6. 从签名恢复公钥
        let recovered_pubkey = match secp.recover_ecdsa(&message, &recoverable_sig) {
            Ok(pubkey) => pubkey,
            Err(e) => {
                println!("错误: 恢复公钥失败 - {:?}", e);
                return ("公钥恢复失败".to_string(), false);
            }
        };

        // 7. 序列化公钥并去除前缀
        let pubkey_serialized = recovered_pubkey.serialize_uncompressed();
        let pubkey_without_prefix = &pubkey_serialized[1..]; // 去除0x04前缀

        // 8. 计算地址 (使用ethers的keccak256)
        let hash = keccak256(pubkey_without_prefix);
        let eth_address = &hash[12..32];
        let recovered_address = format!("0x{}", hex::encode(eth_address));

        // 9. 比较地址
        let is_valid =
            recovered_address.to_lowercase() == format!("0x{}", expected_address).to_lowercase();

        (recovered_address, is_valid)
    }

    // 从DER格式转换为以太坊签名格式
    fn der_to_ethereum_signature(der_hex: &str) -> Result<String, String> {
        // 1. 解析DER格式
        let der_bytes = hex::decode(der_hex).map_err(|e| format!("解码失败: {}", e))?;

        // 验证DER序列标记 (0x30)
        if der_bytes.len() < 8 || der_bytes[0] != 0x30 {
            return Err("无效的DER格式".to_string());
        }

        // 2. 找到R值
        let mut pos = 2; // 跳过序列类型和长度
        if der_bytes[pos] != 0x02 {
            // 整数类型标记
            return Err("无效的R值标记".to_string());
        }
        pos += 1;

        let r_len = der_bytes[pos] as usize;
        pos += 1;
        let mut r = der_bytes[pos..pos + r_len].to_vec();

        // 3. 找到S值
        pos += r_len;
        if der_bytes[pos] != 0x02 {
            // 整数类型标记
            return Err("无效的S值标记".to_string());
        }
        pos += 1;

        let s_len = der_bytes[pos] as usize;
        pos += 1;
        let mut s = der_bytes[pos..pos + s_len].to_vec();

        // 4. 处理前导零并调整至32字节
        // R值处理
        if r.len() > 32 {
            if r[0] == 0 {
                r = r[1..].to_vec();
            } else {
                return Err("R值过长".to_string());
            }
        }
        let mut r_padded = vec![0; 32];
        r_padded[32 - r.len()..].copy_from_slice(&r);

        // S值处理
        if s.len() > 32 {
            if s[0] == 0 {
                s = s[1..].to_vec();
            } else {
                return Err("S值过长".to_string());
            }
        }
        let mut s_padded = vec![0; 32];
        s_padded[32 - s.len()..].copy_from_slice(&s);

        // 5. 添加恢复ID (v)
        // 以太坊使用27或28作为恢复ID
        // 通常需要尝试两个值看哪个能恢复正确地址

        // 返回v=27的签名
        let eth_sig = format!(
            "{}{}{:02x}",
            hex::encode(r_padded),
            hex::encode(s_padded),
            27
        );
        Ok(eth_sig)
    }
}

// 304502210089301dc8da36caad5ed7192d002b6a1c1f85c7aa5589daa2807fbb3a26a647020220661820cf88887c375fd914007e03eeef7897a5ea6a36b8a9f5b8044d6302fd12
// d43fbd6eec3dc592677a6f916836bb36b954a9f89b833af6da63911ce4a9ca7642473bf1e1ae68fd6f4428034e1725b2e208ab88b882584a3ce4a1bb9ff083541b

// privatekey: 4671f59f2800c6bc00c427017618e52ff6f5b3434b41640ab6b745f849654430
// address: ******************************************
// hashed_message: 0e68ea5cf8ed8360a46aafbca3e166694bbab78e145c5dc39c6411d9c4354c99
// signed: d43fbd6eec3dc592677a6f916836bb36b954a9f89b833af6da63911ce4a9ca7642473bf1e1ae68fd6f4428034e1725b2e208ab88b882584a3ce4a1bb9ff083541b
