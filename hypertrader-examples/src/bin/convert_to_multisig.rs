use hypertrader_examples::*;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

// convert TEST_USER3 from single account to multisig account
// with TEST_USER0 and TEST_USER2, the threshold is 1

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER3_SECRET").unwrap();
    let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let res = client
        .convert_to_multi_sig_account(vec![TEST_USER0.to_string(), TEST_USER2.to_string()], 1)
        .await?;
    tracing::info!("res: {res:#?}");

    Ok(())
}
