use hyperliquid_rust_sdk::next_nonce;
use hypertrader_examples::*;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER3_SECRET").unwrap();

    let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let nonce = next_nonce();
    let res = client
        .deposite_usd_to_vault(5000000, SINGLE_ACCOUNT_VAULT_ADDRESS.to_string(), nonce)
        .await?;

    tracing::info!("res: {res:?}");

    Ok(())
}
