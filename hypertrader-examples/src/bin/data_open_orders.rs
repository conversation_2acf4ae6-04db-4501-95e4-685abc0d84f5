use hypertrader_hyperliquid::client::data::DataClient;
use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let agent_vault_address = "0x8ad5d36232016f9385c273b008b6dc40af54bb78";

    let (data_client, _) = DataClient::new(is_testnet).await?;

    // get open orders
    let orders = data_client
        .open_orders(agent_vault_address.parse().unwrap())
        .await?;

    tracing::info!("orders: {orders:#?}");

    // get detailed open orders
    let order_statuses = data_client
        .open_orders_detailed(agent_vault_address.parse().unwrap())
        .await?;
    tracing::info!("order_statuses: {order_statuses:#?}");

    // // cancel order
    // let response = client.vault_order_cancel("XRP", 81684397256).await.unwrap();
    // tracing::info!("cancel response: {response:#?}");

    Ok(())
}
