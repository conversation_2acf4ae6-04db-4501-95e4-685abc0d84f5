use hyperliquid_rust_sdk::{<PERSON>lientLimit, ClientOrder, ClientOrderRequest, next_nonce};
use hypertrader_examples::*;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER3_SECRET").unwrap();
    // let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let client = SingleAccountClient::new_with_vault(
        &secret_key,
        SINGLE_ACCOUNT_VAULT_ADDRESS.parse().unwrap(),
        is_testnet,
    )
    .await;

    let nonce = next_nonce();

    // order:
    let order = ClientOrderRequest {
        asset: "ORDI".to_string(),
        is_buy: true,
        reduce_only: false,
        limit_px: 9.3,
        sz: 2.5,
        cloid: None,
        order_type: ClientOrder::Limit(ClientLimit {
            tif: "Gtc".to_string(),
        }),
    };

    // vault order:
    let res = client.vault_order(order, nonce).await.unwrap();
    tracing::info!("res: {res:#?}");

    Ok(())
}
