use hyperliquid_rust_sdk::ClientLimit;
use hyperliquid_rust_sdk::ClientOrder;
use hyperliquid_rust_sdk::ClientOrderRequest;
use hyperliquid_rust_sdk::next_nonce;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    // agent private key
    let secret_key = "4671f59f2800c6bc00c427017618e52ff6f5b3434b41640ab6b745f849654430";
    let client1 = SingleAccountClient::new(&secret_key, is_testnet).await;
    println!("Current agent account: {}", client1.address());

    let secret_key = "ce8ba13ea2b210c30e4e940cf47c45bcd096d6e1f38badd455df592166dd421f";
    let client2 = SingleAccountClient::new(&secret_key, is_testnet).await;
    println!("Current agent account: {}", client2.address());

    let order = ClientOrderRequest {
        asset: "ORDI".to_string(),
        is_buy: true,
        reduce_only: false,
        limit_px: 7.31,
        sz: 70.0,
        cloid: None,
        order_type: ClientOrder::Limit(ClientLimit {
            tif: "Gtc".to_string(),
        }),
    };

    let res = client1.order(order.clone(), None, next_nonce()).await?;
    println!("Order response: {res:?}");

    // let mut tasks = Vec::new();
    // tasks.push(client1.order(order.clone(), None, next_nonce()));
    // tasks.push(client2.order(order.clone(), None, next_nonce()));

    // let res: Vec<Result<hyperliquid_rust_sdk::ExchangeResponseStatus, anyhow::Error>> =
    //     futures::future::join_all(tasks).await;

    // let res = client1.order(order, None, next_nonce()).await;
    // println!("Order response: {res:?}");

    Ok(())
}
