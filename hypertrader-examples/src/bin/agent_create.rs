use hyperliquid_rust_sdk::LocalWallet;
use hyperliquid_rust_sdk::Signer;
use hypertrader_examples::TEST_USER2;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER2_SECRET").unwrap();
    println!("Origianl account: {}", TEST_USER2);

    let client = SingleAccountClient::new(&secret_key, is_testnet).await;
    let (private_key, response) = client.client.approve_agent(None).await.unwrap();
    println!("Agent creation response: {response:?}");
    println!("\nAgent private_key: {:?}", private_key);
    let wallet: LocalWallet = private_key.parse().unwrap();
    println!("Agent wallet: {:#x}", wallet.address());

    Ok(())
}

// Agent private_key: "4671f59f2800c6bc00c427017618e52ff6f5b3434b41640ab6b745f849654430"
// Agent wallet: ******************************************
