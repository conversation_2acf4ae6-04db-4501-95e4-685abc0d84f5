use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER0_SECRET").unwrap();

    // let destination = "0xe185ba043e95cf71486590e1f187e15589d0934e";
    let destination = "0xf61259aeb4b28e2d99cc61704ceb8b4b988a1109";

    let client = SingleAccountClient::new(&secret_key, is_testnet).await;
    let res = client.usd_send("26.6", destination).await?;

    tracing::info!("res: {res:?}");

    Ok(())
}
