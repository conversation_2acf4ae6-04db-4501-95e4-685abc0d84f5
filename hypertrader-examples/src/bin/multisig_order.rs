use hyperliquid_rust_sdk::{
    Address, ClientLimit, ClientOrder, ClientOrderRequest, LocalWallet, next_nonce,
};
use hypertrader_examples::*;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;
use std::str::FromStr;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER0_SECRET").unwrap();
    let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let test_wallet0: LocalWallet = std::env::var("TEST_USER0_SECRET").unwrap().parse().unwrap();
    let test_wallet2: LocalWallet = std::env::var("TEST_USER2_SECRET").unwrap().parse().unwrap();
    let test_wallet3: LocalWallet = std::env::var("TEST_USER3_SECRET").unwrap().parse().unwrap();
    let authorized_wallets = vec![test_wallet2, test_wallet3, test_wallet0];

    let multi_sig_user = Address::from_str(TEST_USER1).unwrap();
    let out_signer = Address::from_str(TEST_USER0).unwrap();

    let nonce = next_nonce();
    // let nonce = 1743134283193u64;

    // order:
    let order = ClientOrderRequest {
        asset: "ORDI".to_string(),
        is_buy: true,
        reduce_only: false,
        limit_px: 9.3,
        sz: 3.0,
        cloid: None,
        order_type: ClientOrder::Limit(ClientLimit {
            tif: "Gtc".to_string(),
        }),
    };

    // 收集签名
    let mut signatures = Vec::new();
    for wallet in authorized_wallets {
        let signature = client
            .client
            .multi_sig_order_sig(
                vec![order.clone()],
                Some(&wallet),
                multi_sig_user,
                out_signer,
                nonce,
            )
            .await?;

        signatures.push(signature);
        if signatures.len() == 2 {
            break;
        }
    }

    let res = client
        .client
        .multi_sig_order(vec![order], signatures, None, multi_sig_user, nonce)
        .await;

    tracing::info!("res: {res:#?}");

    Ok(())
}
