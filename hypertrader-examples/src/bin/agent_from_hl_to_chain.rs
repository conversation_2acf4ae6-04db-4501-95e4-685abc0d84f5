use hypertrader_examples::TEST_USER2;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = "4671f59f2800c6bc00c427017618e52ff6f5b3434b41640ab6b745f849654430";

    let client = SingleAccountClient::new(&secret_key, is_testnet).await;
    println!("Current agent account: {}", client.address());

    let res = client
        .client
        .withdraw_from_bridge("7.7".to_string(), TEST_USER2.to_string(), None)
        .await?;

    tracing::info!("res: {res:?}");

    Ok(())
}
