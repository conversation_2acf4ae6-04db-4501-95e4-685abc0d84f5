use hyperliquid_rust_sdk::{Address, LocalWallet, next_nonce};
use hypertrader_examples::*;
use hypertrader_hyperliquid::client::single_account::SingleAccountClient;
use hypertrader_utils::init::ensure_inited;
use std::{str::FromStr, vec};

////////////////////////////////////////////////////////////
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
// Not work!
////////////////////////////////////////////////////////////

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    ensure_inited();

    let is_testnet = true;
    let secret_key = std::env::var("TEST_USER0_SECRET").unwrap();
    let client = SingleAccountClient::new(&secret_key, is_testnet).await;

    let test_wallet0: LocalWallet = std::env::var("TEST_USER0_SECRET").unwrap().parse().unwrap();

    let multi_sig_user = Address::from_str(TEST_USER3).unwrap();
    let out_signer = Address::from_str(TEST_USER0).unwrap();

    let nonce = next_nonce();

    let signature = client
        .client
        .convert_to_single_sig(multi_sig_user, out_signer, Some(&test_wallet0), nonce)
        .await?;

    let res = client
        .client
        .convert_to_single(vec![signature], multi_sig_user, Some(&test_wallet0), nonce)
        .await;
    tracing::info!("res: {res:#?}");

    Ok(())
}
