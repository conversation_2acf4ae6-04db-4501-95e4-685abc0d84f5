[package]
name = "hypertrader-examples"
version = "0.1.0"
edition = "2024"

[dependencies]
hypertrader-hyperliquid = { workspace = true }
hypertrader-utils = { workspace = true }
hypertrader-dbs = { workspace = true }
hypertrader-core = { workspace = true }
hypertrader-data = { workspace = true }
hyperliquid_rust_sdk = { workspace = true }

tokio = { workspace = true, features = ["full"] }
anyhow = { workspace = true }
tracing = { workspace = true }
serde_json = { workspace = true, features = ["preserve_order"] }
futures = { workspace = true }
tiny-keccak = "2.0.2"
hex = "0.4.3"
ethers = { version = "2.0", default-features = false }
secp256k1 = { version = "0.28", features = ["recovery"] }
